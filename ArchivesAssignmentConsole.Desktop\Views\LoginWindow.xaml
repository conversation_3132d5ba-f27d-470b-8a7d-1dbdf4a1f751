<Window x:Class="ArchivesAssignmentConsole.Desktop.Views.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        Title="Archives Assignment Console - Login" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="{DynamicResource MaterialDesignPaper}"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        FontFamily="{DynamicResource MaterialDesignFont}">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Margin="20,20,20,10" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Orientation="Vertical" Margin="20">
                <materialDesign:PackIcon Kind="Archive" 
                                       Width="48" Height="48" 
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                <TextBlock Text="Archives Assignment Console" 
                         FontSize="18" FontWeight="Bold"
                         HorizontalAlignment="Center" 
                         Margin="0,10,0,0"/>
                <TextBlock Text="Desktop Application" 
                         FontSize="12" 
                         HorizontalAlignment="Center"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Login Form -->
        <materialDesign:Card Grid.Row="1" Margin="20,10,20,10" 
                           materialDesign:ShadowAssist.ShadowDepth="Depth2">
            <StackPanel Margin="30">
                <TextBlock Text="Sign In" 
                         FontSize="24" FontWeight="Bold" 
                         HorizontalAlignment="Center" 
                         Margin="0,0,0,30"/>

                <!-- Username -->
                <TextBox x:Name="UsernameTextBox"
                       materialDesign:HintAssist.Hint="Username"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,0,20"
                       Text="admin"/>

                <!-- Password -->
                <PasswordBox x:Name="PasswordBox"
                           materialDesign:HintAssist.Hint="Password"
                           Style="{StaticResource MaterialDesignFloatingHintPasswordBox}"
                           Margin="0,0,0,20"/>

                <!-- Two Factor Code (Initially Hidden) -->
                <TextBox x:Name="TwoFactorTextBox"
                       materialDesign:HintAssist.Hint="Two-Factor Code (Optional)"
                       Style="{StaticResource MaterialDesignFloatingHintTextBox}"
                       Margin="0,0,0,20"
                       Visibility="Collapsed"/>

                <!-- Remember Me -->
                <CheckBox x:Name="RememberMeCheckBox"
                        Content="Remember me"
                        Margin="0,0,0,20"/>

                <!-- Login Button -->
                <Button x:Name="LoginButton"
                      Content="SIGN IN"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      materialDesign:ButtonAssist.CornerRadius="20"
                      Height="40"
                      Margin="0,10,0,0"
                      Click="LoginButton_Click"/>

                <!-- Status Message -->
                <TextBlock x:Name="StatusTextBlock"
                         HorizontalAlignment="Center"
                         Margin="0,15,0,0"
                         Foreground="Red"
                         Visibility="Collapsed"/>

                <!-- Loading Indicator -->
                <ProgressBar x:Name="LoadingProgressBar"
                           Style="{StaticResource MaterialDesignCircularProgressBar}"
                           Width="30" Height="30"
                           HorizontalAlignment="Center"
                           Margin="0,15,0,0"
                           Visibility="Collapsed"
                           IsIndeterminate="True"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Footer -->
        <StackPanel Grid.Row="2" Orientation="Vertical" 
                  HorizontalAlignment="Center" 
                  Margin="20,10,20,20">
            <TextBlock Text="Default Login: admin / admin123" 
                     FontSize="11" 
                     HorizontalAlignment="Center"
                     Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5,0,0">
                <materialDesign:PackIcon Kind="Web" Width="16" Height="16" 
                                       VerticalAlignment="Center" Margin="0,0,5,0"/>
                <TextBlock Text="Web Interface: localhost:5004" 
                         FontSize="11"
                         Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </StackPanel>
    </Grid>
</Window>
