using Microsoft.EntityFrameworkCore;
using Serilog;
using ArchivesAssignmentConsole.Core.Data;
using ArchivesAssignmentConsole.Core.Interfaces;
using ArchivesAssignmentConsole.Web.Services;
using ArchivesAssignmentConsole.Web.Hubs;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.IdentityModel.Tokens;
using System.Text;

var builder = WebApplication.CreateBuilder(args);

// Configure Serilog
Log.Logger = new LoggerConfiguration()
    .WriteTo.Console()
    .WriteTo.File("logs/web-.txt", rollingInterval: RollingInterval.Day)
    .CreateLogger();

builder.Host.UseSerilog();

// Add services to the container
builder.Services.AddControllersWithViews()
    .AddRazorRuntimeCompilation();

// Configure Entity Framework
builder.Services.AddDbContext<ArchivesDbContext>(options =>
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection") ?? 
        "Data Source=archives.db"));

// Configure JWT Authentication
var jwtKey = builder.Configuration["Jwt:Key"] ?? "YourSuperSecretKeyForJWTTokenGeneration123456789";
var key = Encoding.ASCII.GetBytes(jwtKey);

builder.Services.AddAuthentication(x =>
{
    x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(x =>
{
    x.RequireHttpsMetadata = false;
    x.SaveToken = true;
    x.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(key),
        ValidateIssuer = false,
        ValidateAudience = false,
        ClockSkew = TimeSpan.Zero
    };
});

// Add Authorization
builder.Services.AddAuthorization();

// Add SignalR for real-time updates
builder.Services.AddSignalR();

// Add Swagger for API documentation
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen();

// Register application services
builder.Services.AddScoped<IAuthenticationService, WebAuthenticationService>();
// TODO: Implement other services
// builder.Services.AddScoped<IUserService, WebUserService>();
// builder.Services.AddScoped<IFileOperationService, WebFileOperationService>();
// builder.Services.AddScoped<IAuditService, WebAuditService>();
// builder.Services.AddScoped<ICrossCheckService, WebCrossCheckService>();
// builder.Services.AddScoped<IPathConfigurationService, WebPathConfigurationService>();
// builder.Services.AddScoped<IEncryptionService, WebEncryptionService>();

// Configure CORS for development
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowAll", policy =>
    {
        policy.AllowAnyOrigin()
              .AllowAnyMethod()
              .AllowAnyHeader();
    });
});

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
    app.UseCors("AllowAll");
}
else
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Configure SignalR Hub
app.MapHub<NotificationHub>("/notificationHub");

// Configure MVC routes
app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");

// Configure API routes
app.MapControllerRoute(
    name: "api",
    pattern: "api/{controller}/{action=Index}/{id?}");

// Ensure database is created and seeded
using (var scope = app.Services.CreateScope())
{
    var context = scope.ServiceProvider.GetRequiredService<ArchivesDbContext>();
    await context.Database.EnsureCreatedAsync();
    
    // TODO: Seed initial data when services are implemented
    // var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
}

// Configure to run on port 5004
app.Urls.Add("http://localhost:5004");
app.Urls.Add("https://localhost:5005");

app.Run();
