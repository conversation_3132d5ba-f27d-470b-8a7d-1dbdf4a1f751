#!/usr/bin/env python3
"""
Archives Assignment Console - Quick Start Script
Run this script to start the application on port 5004
"""

import os
import sys
import subprocess
import platform

def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("❌ Python 3.7 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} detected")
    return True

def install_requirements():
    """Install required packages"""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install packages: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    directories = ['logs', 'static/css', 'static/js', 'templates']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    print("✅ Directories created")

def main():
    """Main function to start the application"""
    print("🚀 Archives Assignment Console - Starting...")
    print("=" * 50)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        print("\n💡 Try installing manually:")
        print("   pip install Flask Flask-SQLAlchemy Flask-Login")
        print("   python app.py")
        sys.exit(1)
    
    print("\n🎯 Starting Archives Assignment Console...")
    print("📍 URL: http://localhost:5004")
    print("👤 Default login: admin / admin123")
    print("🛑 Press Ctrl+C to stop")
    print("=" * 50)
    
    # Start the application
    try:
        from app import app
        app.run(host='0.0.0.0', port=5004, debug=True)
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all files are in the correct location")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n👋 Archives Assignment Console stopped")
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
