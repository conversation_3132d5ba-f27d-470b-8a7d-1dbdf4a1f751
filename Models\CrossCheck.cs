using System.ComponentModel.DataAnnotations;

namespace ArchivesAssignmentConsole.Models
{
    public class CrossCheck
    {
        public int Id { get; set; }
        
        public int FileTaskId { get; set; }
        public virtual FileTask FileTask { get; set; } = null!;
        
        public int AssignedToUserId { get; set; }
        public virtual User AssignedToUser { get; set; } = null!;
        
        public int? ReviewedByUserId { get; set; }
        public virtual User? ReviewedByUser { get; set; }
        
        public CrossCheckStatus Status { get; set; } = CrossCheckStatus.Pending;
        
        public CrossCheckResult? Result { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        [StringLength(2000)]
        public string? ReviewNotes { get; set; }
        
        [StringLength(2000)]
        public string? Feedback { get; set; }
        
        public int? QualityScore { get; set; } // 1-10 scale
        
        public bool RequiresSecondReview { get; set; }
        
        public bool IsSecondReview { get; set; }
        
        public int? FirstReviewId { get; set; }
        public virtual CrossCheck? FirstReview { get; set; }
        
        // Navigation properties
        public virtual ICollection<CrossCheck> SecondReviews { get; set; } = new List<CrossCheck>();
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
    }

    public enum CrossCheckStatus
    {
        Pending = 1,
        Assigned = 2,
        InProgress = 3,
        Completed = 4,
        Escalated = 5
    }

    public enum CrossCheckResult
    {
        Approved = 1,
        Rejected = 2,
        NeedsRevision = 3,
        RequiresSecondReview = 4
    }
}
