{% extends "base.html" %}

{% block title %}User Management - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-users text-primary me-3"></i>
                    User Management
                </h1>
                <a href="{{ url_for('create_user') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Create New User
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>
                        System Users
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>User</th>
                                    <th>Role</th>
                                    <th>Status</th>
                                    <th>2FA</th>
                                    <th>Last Login</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr class="role-{{ user.role.replace('_', '-') }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                <i class="fas fa-user"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ user.full_name }}</h6>
                                                <small class="text-muted">{{ user.username }} • {{ user.email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if user.role == 'main_admin' else 'info' if 'executor' in user.role else 'warning' if 'cross_checker' in user.role else 'secondary' }}">
                                            {{ user.role.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactive</span>
                                        {% endif %}
                                        {% if user.locked_until and user.locked_until > moment.utcnow() %}
                                            <span class="badge bg-warning">Locked</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.requires_two_factor %}
                                            <i class="fas fa-shield-alt text-success" title="2FA Enabled"></i>
                                        {% else %}
                                            <i class="fas fa-shield text-muted" title="2FA Disabled"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login_at %}
                                            <small>{{ user.last_login_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                        {% else %}
                                            <small class="text-muted">Never</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('edit_user', user_id=user.id) }}" 
                                               class="btn btn-outline-primary" title="Edit User">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user.id != current_user.id %}
                                            <button type="button" class="btn btn-outline-danger" 
                                                    onclick="confirmDelete({{ user.id }}, '{{ user.username }}')" 
                                                    title="Delete User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Role Statistics -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Users by Role
                    </h6>
                </div>
                <div class="card-body">
                    {% set role_counts = {} %}
                    {% for user in users %}
                        {% set _ = role_counts.update({user.role: role_counts.get(user.role, 0) + 1}) %}
                    {% endfor %}
                    
                    {% for role, count in role_counts.items() %}
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>{{ role.replace('_', ' ').title() }}</span>
                        <span class="badge bg-secondary">{{ count }}</span>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Security Status
                    </h6>
                </div>
                <div class="card-body">
                    {% set active_users = users | selectattr('is_active') | list | length %}
                    {% set users_with_2fa = users | selectattr('requires_two_factor') | list | length %}
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Total Users</span>
                        <span class="badge bg-primary">{{ users | length }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Active Users</span>
                        <span class="badge bg-success">{{ active_users }}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>2FA Enabled</span>
                        <span class="badge bg-warning">{{ users_with_2fa }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete user <strong id="deleteUsername"></strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-danger">Delete User</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(userId, username) {
    document.getElementById('deleteUsername').textContent = username;
    document.getElementById('deleteForm').action = `/users/${userId}/delete`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
