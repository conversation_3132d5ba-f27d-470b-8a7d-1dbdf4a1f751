using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IAuditService
    {
        Task LogAsync(int userId, AuditAction action, string entityType, int? entityId = null, string? entityName = null, string? details = null, string? ipAddress = null, string? userAgent = null);
        Task LogSecurityEventAsync(int userId, AuditAction action, string details, string? ipAddress = null);
        Task<IEnumerable<AuditLog>> GetAuditLogsAsync(int? userId = null, AuditAction? action = null, DateTime? fromDate = null, DateTime? toDate = null, int pageSize = 100, int pageNumber = 1);
        Task<IEnumerable<AuditLog>> GetSecurityEventsAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<AuditLog>> GetUserActivityAsync(int userId, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<AuditLog>> GetFileTaskAuditAsync(int fileTaskId);
        Task<IEnumerable<AuditLog>> GetCrossCheckAuditAsync(int crossCheckId);
        Task<Dictionary<AuditAction, int>> GetAuditSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task CleanupOldLogsAsync(TimeSpan retentionPeriod);
        Task<bool> ExportAuditLogsAsync(string filePath, DateTime? fromDate = null, DateTime? toDate = null);
    }
}
