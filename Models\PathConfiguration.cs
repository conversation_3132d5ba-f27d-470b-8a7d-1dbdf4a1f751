using System.ComponentModel.DataAnnotations;

namespace ArchivesAssignmentConsole.Models
{
    public class PathConfiguration
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string Path { get; set; } = string.Empty;
        
        public PathType Type { get; set; }
        
        public DestinationCategory? Category { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        [StringLength(200)]
        public string? Description { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? ModifiedAt { get; set; }
        
        public int CreatedByUserId { get; set; }
        public virtual User CreatedByUser { get; set; } = null!;
        
        public int? ModifiedByUserId { get; set; }
        public virtual User? ModifiedByUser { get; set; }
    }

    public enum PathType
    {
        Source = 1,
        Destination = 2,
        Backup = 3,
        Temporary = 4
    }
}
