// Archives Assignment Console - JavaScript Application

// Global application object
window.ArchivesConsole = {
    // Configuration
    config: {
        autoRefreshInterval: 30000, // 30 seconds
        apiBaseUrl: '/api',
        supportedVideoFormats: ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp']
    },
    
    // Initialize application
    init: function() {
        console.log('Archives Assignment Console - Initializing...');
        
        // Initialize components
        this.initializeTooltips();
        this.initializePopovers();
        this.setupEventListeners();
        this.startAutoRefresh();
        
        console.log('Archives Assignment Console - Ready!');
    },
    
    // Initialize Bootstrap tooltips
    initializeTooltips: function() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },
    
    // Initialize Bootstrap popovers
    initializePopovers: function() {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions with loading states
        document.addEventListener('submit', function(e) {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                submitBtn.disabled = true;
                
                // Re-enable after 5 seconds as fallback
                setTimeout(function() {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 5000);
            }
        });
        
        // Handle AJAX errors globally
        document.addEventListener('ajaxError', function(e) {
            console.error('AJAX Error:', e.detail);
            ArchivesConsole.showNotification('An error occurred. Please try again.', 'error');
        });
    },
    
    // Start auto-refresh for dashboard
    startAutoRefresh: function() {
        if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
            setInterval(function() {
                ArchivesConsole.refreshDashboardStats();
            }, this.config.autoRefreshInterval);
        }
    },
    
    // Refresh dashboard statistics
    refreshDashboardStats: function() {
        // TODO: Implement AJAX call to refresh dashboard data
        console.log('Refreshing dashboard statistics...');
        
        // Example implementation:
        // fetch('/api/dashboard/stats')
        //     .then(response => response.json())
        //     .then(data => {
        //         // Update dashboard elements
        //         this.updateDashboardElements(data);
        //     })
        //     .catch(error => {
        //         console.error('Error refreshing dashboard:', error);
        //     });
    },
    
    // Show notification toast
    showNotification: function(message, type = 'info', duration = 5000) {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${this.getIconForType(type)} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;
        
        // Create toast container if it doesn't exist
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }
        
        // Add toast
        container.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = container.lastElementChild;
        const toast = new bootstrap.Toast(toastElement, { delay: duration });
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    },
    
    // Get icon for notification type
    getIconForType: function(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // File management functions
    files: {
        // Browse files in directory
        browse: function(path) {
            console.log('Browsing files in:', path);
            // TODO: Implement file browsing
            ArchivesConsole.showNotification('File browser will be implemented in the next phase.', 'info');
        },
        
        // Select file
        select: function(filePath) {
            console.log('Selected file:', filePath);
            // TODO: Implement file selection
        },
        
        // Check if file is video
        isVideo: function(filename) {
            const ext = filename.toLowerCase().substring(filename.lastIndexOf('.'));
            return ArchivesConsole.config.supportedVideoFormats.includes(ext);
        },
        
        // Format file size
        formatSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    },
    
    // Media player functions
    media: {
        // Initialize media player
        init: function(containerId) {
            console.log('Initializing media player in:', containerId);
            // TODO: Implement web-based media player
            ArchivesConsole.showNotification('Media player will be implemented in the next phase.', 'info');
        },
        
        // Play video
        play: function(videoPath) {
            console.log('Playing video:', videoPath);
            // TODO: Implement video playback
        },
        
        // Pause video
        pause: function() {
            console.log('Pausing video');
            // TODO: Implement video pause
        },
        
        // Stop video
        stop: function() {
            console.log('Stopping video');
            // TODO: Implement video stop
        }
    },
    
    // Task management functions
    tasks: {
        // Create new task
        create: function(taskData) {
            console.log('Creating task:', taskData);
            // TODO: Implement task creation
            ArchivesConsole.showNotification('Task creation will be implemented in the next phase.', 'info');
        },
        
        // Update task status
        updateStatus: function(taskId, status) {
            console.log('Updating task', taskId, 'to status:', status);
            // TODO: Implement task status update
        },
        
        // Assign task to user
        assign: function(taskId, userId) {
            console.log('Assigning task', taskId, 'to user:', userId);
            // TODO: Implement task assignment
        },
        
        // Get tasks for user
        getForUser: function(userId) {
            console.log('Getting tasks for user:', userId);
            // TODO: Implement get user tasks
        }
    },
    
    // Cross-check functions
    crossCheck: {
        // Start cross-check
        start: function(taskId) {
            console.log('Starting cross-check for task:', taskId);
            // TODO: Implement cross-check start
            ArchivesConsole.showNotification('Cross-check functionality will be implemented in the next phase.', 'info');
        },
        
        // Complete cross-check
        complete: function(crossCheckId, result, notes) {
            console.log('Completing cross-check', crossCheckId, 'with result:', result);
            // TODO: Implement cross-check completion
        },
        
        // Get pending cross-checks
        getPending: function() {
            console.log('Getting pending cross-checks');
            // TODO: Implement get pending cross-checks
        }
    },
    
    // User management functions
    users: {
        // Create user
        create: function(userData) {
            console.log('Creating user:', userData);
            // TODO: Implement user creation
            ArchivesConsole.showNotification('User management will be implemented in the next phase.', 'info');
        },
        
        // Update user role
        updateRole: function(userId, role) {
            console.log('Updating user', userId, 'to role:', role);
            // TODO: Implement user role update
        },
        
        // Get users by role
        getByRole: function(role) {
            console.log('Getting users by role:', role);
            // TODO: Implement get users by role
        }
    },
    
    // Utility functions
    utils: {
        // Format date/time
        formatDateTime: function(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString();
        },
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },
        
        // Generate random ID
        generateId: function() {
            return Math.random().toString(36).substr(2, 9);
        },
        
        // Validate email
        validateEmail: function(email) {
            const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return re.test(email);
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ArchivesConsole.init();
});

// Global functions for backward compatibility
function showComingSoon(feature) {
    ArchivesConsole.showNotification(`${feature} functionality will be implemented in the next development phase. The foundation is complete and ready for feature implementation!`, 'info');
}

function refreshDashboard() {
    ArchivesConsole.refreshDashboardStats();
}

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ArchivesConsole;
}
