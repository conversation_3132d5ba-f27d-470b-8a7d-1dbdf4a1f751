using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Windows;
using ArchivesAssignmentConsole.Core.Data;
using ArchivesAssignmentConsole.Core.Interfaces;
using ArchivesAssignmentConsole.Desktop.Services;
using ArchivesAssignmentConsole.Desktop.ViewModels;
using LibVLCSharp.Shared;

namespace ArchivesAssignmentConsole.Desktop
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // Initialize LibVLC
            Core.Initialize();

            // Configure logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/desktop-.txt", rollingInterval: RollingInterval.Day)
                .WriteTo.Console()
                .CreateLogger();

            // Build configuration
            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppDomain.CurrentDomain.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();

            // Build host
            _host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    ConfigureServices(services, configuration);
                })
                .Build();

            // Initialize database
            using (var scope = _host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<ArchivesDbContext>();
                await context.Database.EnsureCreatedAsync();
                
                // Seed initial data if needed
                var userService = scope.ServiceProvider.GetRequiredService<IUserService>();
                var users = await userService.GetAllUsersAsync();
                if (!users.Any())
                {
                    // Create default admin user
                    var adminUser = new ArchivesAssignmentConsole.Core.Models.User
                    {
                        Username = "admin",
                        FullName = "System Administrator",
                        Email = "<EMAIL>",
                        Role = ArchivesAssignmentConsole.Core.Models.UserRole.MainAdmin,
                        IsActive = true,
                        RequiresTwoFactor = false
                    };
                    await userService.CreateUserAsync(adminUser, "admin123");
                }
            }

            await _host.StartAsync();
            base.OnStartup(e);
        }

        private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // Configuration
            services.AddSingleton(configuration);

            // Database
            services.AddDbContext<ArchivesDbContext>(options =>
                options.UseSqlite(configuration.GetConnectionString("DefaultConnection") ?? 
                    "Data Source=archives.db"));

            // Core Services
            services.AddSingleton<IAuthenticationService, DesktopAuthenticationService>();
            services.AddSingleton<IUserService, DesktopUserService>();
            services.AddSingleton<IFileOperationService, DesktopFileOperationService>();
            services.AddSingleton<IAuditService, DesktopAuditService>();
            services.AddSingleton<IEncryptionService, DesktopEncryptionService>();
            services.AddSingleton<IPathConfigurationService, DesktopPathConfigurationService>();
            services.AddSingleton<ICrossCheckService, DesktopCrossCheckService>();

            // Desktop-specific services
            services.AddSingleton<IVlcService, VlcService>();
            services.AddSingleton<IDialogService, DialogService>();
            services.AddSingleton<INavigationService, NavigationService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainAdminViewModel>();
            services.AddTransient<ExecutorPublicViewModel>();
            services.AddTransient<ExecutorPrivateViewModel>();
            services.AddTransient<CrossCheckerPublicViewModel>();
            services.AddTransient<CrossCheckerPrivateViewModel>();
            services.AddTransient<ViewerViewModel>();
            services.AddTransient<VlcPlayerViewModel>();
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
