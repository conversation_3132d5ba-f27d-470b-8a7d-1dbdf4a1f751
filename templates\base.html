<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{% block title %}Archives Assignment Console{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    {% if current_user.is_authenticated %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-archive me-2"></i>
                Archives Assignment Console
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    
                    {% if current_user.role in ['executor_public', 'executor_private', 'main_admin'] %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-tasks me-1"></i>Tasks
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('files') }}"><i class="fas fa-plus me-1"></i>Create Task</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('tasks') }}"><i class="fas fa-list me-1"></i>My Tasks</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('tasks') }}"><i class="fas fa-clock me-1"></i>Pending Tasks</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    {% if current_user.role in ['cross_checker_public', 'cross_checker_private', 'main_admin'] %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-check-double me-1"></i>Cross-Check
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('cross_checks') }}"><i class="fas fa-clipboard-check me-1"></i>My Reviews</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('cross_checks') }}"><i class="fas fa-hourglass-half me-1"></i>Pending Reviews</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports') }}"><i class="fas fa-chart-bar me-1"></i>Quality Reports</a></li>
                        </ul>
                    </li>
                    {% endif %}
                    
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('files') }}">
                            <i class="fas fa-play-circle me-1"></i>Media Player
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('files') }}">
                            <i class="fas fa-folder-open me-1"></i>File Browser
                        </a>
                    </li>
                    
                    {% if current_user.role == 'main_admin' %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cogs me-1"></i>Admin
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('users') }}"><i class="fas fa-users me-1"></i>User Management</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports') }}"><i class="fas fa-folder-cog me-1"></i>Path Configuration</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports') }}"><i class="fas fa-chart-line me-1"></i>Reports</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('audit_logs') }}"><i class="fas fa-shield-alt me-1"></i>Security</a></li>
                        </ul>
                    </li>
                    {% endif %}
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>{{ current_user.full_name }}
                            <span class="badge bg-secondary ms-1">{{ current_user.role.replace('_', ' ').title() }}</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-user-cog me-1"></i>Profile</a></li>
                            <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="fas fa-key me-1"></i>Change Password</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-1"></i>Logout</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            <div class="container-fluid mt-3">
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                        {% elif category == 'error' %}
                            <i class="fas fa-exclamation-triangle me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                        {% else %}
                            <i class="fas fa-info-circle me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% endwith %}

    <!-- Main Content -->
    <main class="{% if current_user.is_authenticated %}container-fluid mt-4{% endif %}">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% if current_user.is_authenticated %}
    <footer class="bg-light text-center text-muted py-3 mt-5">
        <div class="container">
            <small>
                &copy; 2025 Archives Assignment Console - Running on Port 5004 - 
                <span id="current-time"></span>
            </small>
        </div>
    </footer>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    
    {% block extra_js %}{% endblock %}

    <script>
        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        
        // Update time every second
        if (document.getElementById('current-time')) {
            updateTime();
            setInterval(updateTime, 1000);
        }
        
        // Auto-dismiss alerts after 5 seconds
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    </script>
</body>
</html>
