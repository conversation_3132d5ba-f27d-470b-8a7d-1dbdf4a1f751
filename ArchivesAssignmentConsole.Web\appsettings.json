{"ConnectionStrings": {"DefaultConnection": "Data Source=archives.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyForJWTTokenGeneration123456789", "Issuer": "ArchivesAssignmentConsole", "Audience": "ArchivesUsers", "ExpiryInHours": 24}, "DefaultPaths": {"SourcePath": "T:\\To_Process\\Rough folder\\To Process\\restore Files", "Destinations": {"Miscellaneous": "T:\\To_Process\\Rough folder\\To Check\\Miscellaneous", "PrivateOne": "T:\\To_Process\\Rough folder\\To Check\\Private one video", "PrivateTwo": "T:\\To_Process\\Rough folder\\To Check\\Private Two", "ToBeProcessed": "T:\\To_Process\\Rough folder\\To Process\\restore Files"}}, "Security": {"EncryptionKey": "YourSecureEncryptionKeyHere_32Chars", "RequireTwoFactorForPrivate": true, "SessionTimeoutMinutes": 60, "MaxLoginAttempts": 3, "LockoutDurationMinutes": 15}, "CrossCheck": {"RequiredForPrivateContent": true, "DualCheckForCritical": true, "AutoAssignmentEnabled": true}, "VideoPlayer": {"SupportedFormats": ["mp4", "avi", "mkv", "mov", "wmv", "flv", "webm"], "MaxFileSize": 2147483648, "StreamingEnabled": true, "ThumbnailGeneration": true}}