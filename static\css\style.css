/* Archives Assignment Console - Custom Styles */

:root {
    --primary-color: #667eea;
    --primary-dark: #764ba2;
    --secondary-color: #f093fb;
    --success-color: #4facfe;
    --warning-color: #f6d365;
    --danger-color: #fa709a;
    --info-color: #43e97b;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f7fa;
}

/* Navigation */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* Cards */
.card {
    border: none;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
}

/* Buttons */
.btn {
    border-radius: 10px;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border: none;
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-color: var(--primary-color);
}

/* Progress bars */
.progress {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
}

.progress-bar {
    border-radius: 10px;
}

/* Badges */
.badge {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.5em 0.75em;
}

/* Alerts */
.alert {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.alert-info {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    color: #0d47a1;
}

.alert-success {
    background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
    color: #1b5e20;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
    color: #e65100;
}

.alert-danger {
    background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
    color: #c62828;
}

/* Dashboard specific styles */
.display-5 {
    font-weight: 300;
    color: var(--dark-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

/* Statistics cards */
.card-body h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

/* List groups */
.list-group-item {
    border: none;
    padding: 0.75rem 0;
    background: transparent;
}

.list-group-item:not(:last-child) {
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Role-specific colors */
.role-main-admin { border-left: 4px solid #6f42c1; }
.role-executor-public { border-left: 4px solid #17a2b8; }
.role-executor-private { border-left: 4px solid #dc3545; }
.role-cross-checker-public { border-left: 4px solid #28a745; }
.role-cross-checker-private { border-left: 4px solid #fd7e14; }
.role-viewer { border-left: 4px solid #6c757d; }

/* Status colors */
.status-pending { color: #ffc107; }
.status-in-progress { color: #17a2b8; }
.status-completed { color: #28a745; }
.status-cross-check { color: #fd7e14; }
.status-approved { color: #28a745; }
.status-rejected { color: #dc3545; }

/* Media player styles */
.media-player-container {
    background: #000;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
}

.video-controls {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 1rem;
}

/* File browser styles */
.file-browser {
    max-height: 400px;
    overflow-y: auto;
    border-radius: 10px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.file-item {
    padding: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-item:hover {
    background: rgba(102, 126, 234, 0.1);
    transform: translateX(5px);
}

.file-item:last-child {
    border-bottom: none;
}

.file-icon {
    width: 24px;
    text-align: center;
    margin-right: 1rem;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .display-5 {
        font-size: 2rem;
    }
    
    .card-body h3 {
        font-size: 2rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .btn {
        padding: 0.4rem 1rem;
        font-size: 0.9rem;
    }
}

/* Loading spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* Footer */
footer {
    margin-top: auto;
}

/* Utility classes */
.shadow-sm {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08) !important;
}

.border-0 {
    border: none !important;
}

.rounded-lg {
    border-radius: 15px !important;
}

/* Dark mode support (future enhancement) */
@media (prefers-color-scheme: dark) {
    :root {
        --bs-body-bg: #1a1a1a;
        --bs-body-color: #ffffff;
    }
}
