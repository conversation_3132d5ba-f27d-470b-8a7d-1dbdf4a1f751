using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using Serilog;
using System.Windows;
using ArchivesAssignmentConsole.Data;
using ArchivesAssignmentConsole.Services;
using ArchivesAssignmentConsole.ViewModels;
using LibVLCSharp.Shared;

namespace ArchivesAssignmentConsole
{
    public partial class App : Application
    {
        private IHost? _host;

        protected override async void OnStartup(StartupEventArgs e)
        {
            // Initialize LibVLC
            Core.Initialize();

            // Configure logging
            Log.Logger = new LoggerConfiguration()
                .WriteTo.File("logs/app-.txt", rollingInterval: RollingInterval.Day)
                .CreateLogger();

            // Build host
            _host = Host.CreateDefaultBuilder()
                .UseSerilog()
                .ConfigureServices((context, services) =>
                {
                    ConfigureServices(services, context.Configuration);
                })
                .Build();

            // Initialize database
            using (var scope = _host.Services.CreateScope())
            {
                var context = scope.ServiceProvider.GetRequiredService<ArchivesDbContext>();
                await context.Database.EnsureCreatedAsync();
                
                // Seed initial data
                var seedService = scope.ServiceProvider.GetRequiredService<IDataSeedService>();
                await seedService.SeedInitialDataAsync();
            }

            await _host.StartAsync();
            base.OnStartup(e);
        }

        private void ConfigureServices(IServiceCollection services, IConfiguration configuration)
        {
            // Database
            services.AddDbContext<ArchivesDbContext>(options =>
                options.UseSqlite(configuration.GetConnectionString("DefaultConnection") ?? 
                    "Data Source=archives.db"));

            // Services
            services.AddSingleton<IAuthenticationService, AuthenticationService>();
            services.AddSingleton<IUserService, UserService>();
            services.AddSingleton<IFileOperationService, FileOperationService>();
            services.AddSingleton<IAuditService, AuditService>();
            services.AddSingleton<IEncryptionService, EncryptionService>();
            services.AddSingleton<IPathConfigurationService, PathConfigurationService>();
            services.AddSingleton<ICrossCheckService, CrossCheckService>();
            services.AddSingleton<IDataSeedService, DataSeedService>();

            // ViewModels
            services.AddTransient<LoginViewModel>();
            services.AddTransient<MainAdminViewModel>();
            services.AddTransient<ExecutorPublicViewModel>();
            services.AddTransient<ExecutorPrivateViewModel>();
            services.AddTransient<CrossCheckerPublicViewModel>();
            services.AddTransient<CrossCheckerPrivateViewModel>();
            services.AddTransient<ViewerViewModel>();
        }

        protected override async void OnExit(ExitEventArgs e)
        {
            if (_host != null)
            {
                await _host.StopAsync();
                _host.Dispose();
            }
            Log.CloseAndFlush();
            base.OnExit(e);
        }
    }
}
