{% extends "base.html" %}

{% block title %}Task Management - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-tasks text-primary me-3"></i>
                    {% if current_user.role == 'main_admin' %}
                        All Tasks
                    {% elif current_user.role in ['executor_public', 'executor_private'] %}
                        My Tasks
                    {% elif current_user.role in ['cross_checker_public', 'cross_checker_private'] %}
                        Tasks for Review
                    {% else %}
                        System Tasks
                    {% endif %}
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshTasks()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <a href="{{ url_for('files') }}" class="btn btn-primary">
                        <i class="fas fa-folder-open me-1"></i>Browse Files
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Task Statistics -->
    <div class="row mb-4">
        {% set pending_count = tasks | selectattr('status', 'equalto', 'pending') | list | length %}
        {% set in_progress_count = tasks | selectattr('status', 'equalto', 'in_progress') | list | length %}
        {% set completed_count = tasks | selectattr('status', 'equalto', 'completed') | list | length %}
        {% set cross_check_count = tasks | selectattr('status', 'in', ['cross_check_required', 'cross_check_in_progress']) | list | length %}
        
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ pending_count }}</h3>
                    <p class="mb-0">Pending</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-play fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ in_progress_count }}</h3>
                    <p class="mb-0">In Progress</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ completed_count }}</h3>
                    <p class="mb-0">Completed</p>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-double fa-2x text-secondary mb-2"></i>
                    <h3 class="text-secondary">{{ cross_check_count }}</h3>
                    <p class="mb-0">Cross-Check</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tasks List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Tasks
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary" onclick="filterTasks('all')">All</button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterTasks('pending')">Pending</button>
                            <button type="button" class="btn btn-outline-info" onclick="filterTasks('in_progress')">In Progress</button>
                            <button type="button" class="btn btn-outline-success" onclick="filterTasks('completed')">Completed</button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>File</th>
                                    <th>Category</th>
                                    <th>Status</th>
                                    <th>Priority</th>
                                    <th>Assigned To</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for task in tasks %}
                                <tr class="task-row" data-status="{{ task.status }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-video text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0">{{ task.file_name[:40] }}{% if task.file_name|length > 40 %}...{% endif %}</h6>
                                                <small class="text-muted">
                                                    {{ (task.file_size / 1024 / 1024) | round(2) }} MB
                                                    {% if task.is_private_content %}
                                                    <span class="badge bg-danger ms-1">Private</span>
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if 'private' in task.category else 'info' }}">
                                            {{ task.category.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if task.status == 'pending' else 'info' if task.status == 'in_progress' else 'success' if task.status in ['completed', 'approved'] else 'danger' if task.status == 'rejected' else 'secondary' }}">
                                            {{ task.status.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if task.priority == 'critical' else 'warning' if task.priority == 'high' else 'info' if task.priority == 'normal' else 'secondary' }}">
                                            {{ task.priority.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if task.assigned_user %}
                                            <small>{{ task.assigned_user.full_name }}</small>
                                        {% else %}
                                            <small class="text-muted">Unassigned</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('task_detail', task_id=task.id) }}" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if task.source_path and task.file_name %}
                                            <a href="{{ url_for('media_player', file=task.source_path + '\\' + task.file_name) }}" 
                                               class="btn btn-outline-success" title="Play Video">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            {% endif %}
                                            {% if current_user.role == 'main_admin' or (task.assigned_to_user_id == current_user.id and task.status in ['assigned', 'in_progress']) %}
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="updateTaskStatus({{ task.id }}, '{{ task.status }}')" 
                                                    title="Update Status">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not tasks %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-tasks text-muted fa-3x mb-3"></i>
                                        <h5 class="text-muted">No tasks found</h5>
                                        <p class="text-muted">
                                            {% if current_user.role in ['executor_public', 'executor_private'] %}
                                                No tasks have been assigned to you yet.
                                            {% elif current_user.role in ['cross_checker_public', 'cross_checker_private'] %}
                                                No tasks are pending your review.
                                            {% else %}
                                                No tasks have been created yet.
                                            {% endif %}
                                        </p>
                                        <a href="{{ url_for('files') }}" class="btn btn-primary">
                                            <i class="fas fa-plus me-2"></i>Create First Task
                                        </a>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Task Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="updateStatusForm" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newStatus" class="form-label">New Status</label>
                        <select class="form-select" id="newStatus" name="status" required>
                            <option value="">Select Status</option>
                            <option value="pending">Pending</option>
                            <option value="in_progress">In Progress</option>
                            <option value="completed">Completed</option>
                            <option value="on_hold">On Hold</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="statusNotes" class="form-label">Notes</label>
                        <textarea class="form-control" id="statusNotes" name="notes" rows="3" 
                                  placeholder="Add any notes about this status change..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Status</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshTasks() {
    window.location.reload();
}

function filterTasks(status) {
    const rows = document.querySelectorAll('.task-row');
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update active button
    document.querySelectorAll('.btn-group-sm .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function updateTaskStatus(taskId, currentStatus) {
    document.getElementById('updateStatusForm').action = `/tasks/${taskId}/update-status`;
    document.getElementById('newStatus').value = currentStatus;
    new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
}

// Auto-refresh every 30 seconds
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        refreshTasks();
    }
}, 30000);
</script>
{% endblock %}
