# Archives Assignment Console - Flask Edition

🎉 **Now running on Python Flask for easy setup and deployment on port 5004!**

## Quick Start (3 Steps)

### 1. Install Python
- Download Python 3.7+ from: https://www.python.org/downloads/
- Make sure to check "Add Python to PATH" during installation

### 2. Run the Application
**Option A: Use the batch file (Windows)**
```bash
start.bat
```

**Option B: Use Python directly**
```bash
pip install -r requirements.txt
python app.py
```

**Option C: Use the run script**
```bash
python run.py
```

### 3. Access the Application
- **Web Interface:** http://localhost:5004
- **Default Login:** admin / admin123

## Features

### ✅ **Currently Working**
- **Flask Web Application** running on port 5004
- **User Authentication** with session management
- **Role-Based Access Control** (6 user roles)
- **SQLite Database** with automatic setup
- **Dashboard** with real-time statistics
- **Responsive UI** with Bootstrap 5 and custom styling
- **Audit Logging** for security and compliance
- **Modern Design** with gradients and animations

### 🚧 **Ready for Implementation**
- **File Browser** - Browse and manage video files
- **Media Player** - Web-based video player
- **Task Management** - Create, assign, and track tasks
- **Cross-Checking** - Quality assurance workflows
- **User Management** - Admin interface for users
- **Path Configuration** - Manage source and destination paths
- **Reports** - Analytics and quality reports

## User Roles

1. **Main Admin/Assigner** - Full system access and user management
2. **Executor Public** - Process non-sensitive video content
3. **Executor Private** - Process sensitive content with enhanced security
4. **Cross-Checker Public** - Quality assurance for public content
5. **Cross-Checker Private** - Quality assurance for private content with 2FA
6. **Viewer** - Read-only access to dashboards and reports

## Project Structure

```
Archives Assignment Console/
├── app.py                  # Main Flask application
├── requirements.txt        # Python dependencies
├── run.py                 # Quick start script
├── start.bat              # Windows batch file
├── templates/             # HTML templates
│   ├── base.html         # Base template with navigation
│   ├── login.html        # Login page
│   └── dashboard.html    # Main dashboard
├── static/               # Static files
│   ├── css/style.css    # Custom styles
│   └── js/app.js        # JavaScript application
├── logs/                 # Application logs
└── archives.db           # SQLite database (auto-created)
```

## Configuration

### Default Paths
The application is configured for these default paths (editable in app.py):

```
T:\To_Process\Rough folder\
├── To Process\restore Files\          # Source folder
└── To Check\
    ├── Miscellaneous\                 # Public destination
    ├── Private one video\             # Private destination 1
    └── Private Two\                   # Private destination 2
```

**Note:** The application will work even if these paths don't exist. You can configure different paths later.

### Database
- **Type:** SQLite (archives.db)
- **Auto-created:** Yes, on first run
- **Default Admin:** username: `admin`, password: `admin123`

## Development

### Adding New Features
1. **Routes:** Add new routes in `app.py`
2. **Templates:** Create HTML templates in `templates/`
3. **Models:** Add database models in `app.py`
4. **Static Files:** Add CSS/JS in `static/`

### Database Models
- **User** - User accounts and authentication
- **FileTask** - File processing tasks
- **CrossCheck** - Quality assurance reviews
- **AuditLog** - Security and activity logging
- **PathConfiguration** - System path settings

## API Endpoints (Future)

The application is structured to easily add REST API endpoints:

```
GET  /api/tasks           # Get tasks
POST /api/tasks           # Create task
GET  /api/users           # Get users
POST /api/cross-checks    # Create cross-check
GET  /api/dashboard/stats # Dashboard statistics
```

## Security Features

- **Password Hashing** with Werkzeug
- **Session Management** with Flask-Login
- **Failed Login Protection** with account lockout
- **Audit Logging** for all user actions
- **Role-Based Permissions** for different user types
- **CSRF Protection** (ready to implement)

## Deployment Options

### Development
```bash
python app.py  # Runs on localhost:5004
```

### Production
```bash
# Using Gunicorn
pip install gunicorn
gunicorn -w 4 -b 0.0.0.0:5004 app:app

# Using Waitress (Windows-friendly)
pip install waitress
waitress-serve --host=0.0.0.0 --port=5004 app:app
```

### Docker (Future)
```dockerfile
FROM python:3.9-slim
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
EXPOSE 5004
CMD ["python", "app.py"]
```

## Troubleshooting

### Common Issues

**1. "Python not found"**
- Install Python from python.org
- Make sure "Add to PATH" was checked during installation
- Restart command prompt

**2. "pip not found"**
- Python 3.4+ includes pip by default
- Try: `python -m pip install -r requirements.txt`

**3. "Port 5004 already in use"**
- Change port in `app.py`: `app.run(port=5005)`
- Or kill the process using the port

**4. "Module not found"**
- Run: `pip install -r requirements.txt`
- Make sure you're in the correct directory

**5. Database errors**
- Delete `archives.db` file to reset database
- Restart the application

### Logs
Check `logs/app.log` for detailed error information.

## Next Development Phase

The foundation is complete! Ready to implement:

1. **File Management System**
   - Directory browsing
   - File upload/download
   - Batch operations

2. **Media Player Integration**
   - HTML5 video player
   - Playlist management
   - Playback controls

3. **Advanced Workflows**
   - Task assignment algorithms
   - Cross-checking automation
   - Quality scoring

4. **Real-time Features**
   - WebSocket notifications
   - Live status updates
   - Chat/messaging

5. **Advanced Security**
   - Two-factor authentication
   - File encryption
   - Advanced audit reports

## Why Flask?

✅ **Easy Setup** - No .NET SDK required  
✅ **Cross-Platform** - Works on Windows, Mac, Linux  
✅ **Lightweight** - Fast startup and low resource usage  
✅ **Extensible** - Easy to add new features  
✅ **Popular** - Large community and ecosystem  
✅ **Production Ready** - Used by major companies  

---

**🚀 Your Archives Assignment Console is now ready to run on port 5004!**

Just run `start.bat` or `python app.py` and visit http://localhost:5004
