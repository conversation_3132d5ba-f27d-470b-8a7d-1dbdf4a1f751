using Microsoft.AspNetCore.SignalR;

namespace ArchivesAssignmentConsole.Web.Hubs
{
    public class NotificationHub : Hub
    {
        public async Task JoinGroup(string groupName)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
        }

        public async Task LeaveGroup(string groupName)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
        }

        public async Task SendMessage(string user, string message)
        {
            await Clients.All.SendAsync("ReceiveMessage", user, message);
        }

        public async Task SendTaskUpdate(string taskId, string status)
        {
            await Clients.All.SendAsync("TaskUpdated", taskId, status);
        }

        public async Task SendCrossCheckUpdate(string crossCheckId, string status)
        {
            await Clients.All.SendAsync("CrossCheckUpdated", crossCheckId, status);
        }

        public async Task SendUserNotification(string userId, string message, string type = "info")
        {
            await Clients.Group($"user_{userId}").SendAsync("UserNotification", message, type);
        }

        public override async Task OnConnectedAsync()
        {
            // TODO: Add user to appropriate groups based on their role
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            // TODO: Remove user from groups and clean up
            await base.OnDisconnectedAsync(exception);
        }
    }
}
