<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <ApplicationIcon>Resources\app.ico</ApplicationIcon>
    <AssemblyTitle>Archives Assignment Console Desktop</AssemblyTitle>
    <AssemblyDescription>Desktop WPF application for Archives Assignment Console with embedded VLC player</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <Company>Archives Management System</Company>
    <Product>Archives Assignment Console</Product>
    <Copyright>Copyright © 2025</Copyright>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="LibVLCSharp" Version="3.8.5" />
    <PackageReference Include="LibVLCSharp.WPF" Version="3.8.5" />
    <PackageReference Include="VideoLAN.LibVLC.Windows" Version="3.0.20" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.77" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ArchivesAssignmentConsole.Core\ArchivesAssignmentConsole.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="ViewModels\" />
    <Folder Include="Views\" />
    <Folder Include="Services\" />
    <Folder Include="Controls\" />
    <Folder Include="Converters\" />
    <Folder Include="Helpers\" />
    <Folder Include="Resources\" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

</Project>
