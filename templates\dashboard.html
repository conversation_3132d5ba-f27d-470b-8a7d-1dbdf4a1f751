{% extends "base.html" %}

{% block title %}Dashboard - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 mb-1">
                        <i class="fas fa-tachometer-alt text-primary me-3"></i>
                        Dashboard
                    </h1>
                    <p class="text-muted mb-0">Welcome back, {{ current_user.full_name }} ({{ user_role_display }})</p>
                </div>
                <div class="text-end">
                    <div class="badge bg-success fs-6 mb-1">
                        <i class="fas fa-circle me-1"></i>Online
                    </div>
                    <div class="small text-muted">Port 5004</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-primary mb-1">{{ total_users }}</h3>
                            <p class="text-muted mb-0">Active Users</p>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-info mb-1">{{ total_tasks }}</h3>
                            <p class="text-muted mb-0">Total Tasks</p>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-tasks fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-warning mb-1">{{ pending_tasks }}</h3>
                            <p class="text-muted mb-0">Pending Tasks</p>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-success mb-1">{{ completed_tasks }}</h3>
                            <p class="text-muted mb-0">Completed Tasks</p>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Task Overview -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Task Status Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Progress Bars -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Pending</span>
                                    <span class="text-muted">{{ pending_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ (pending_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">In Progress</span>
                                    <span class="text-muted">{{ in_progress_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: {{ (in_progress_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Completed</span>
                                    <span class="text-muted">{{ completed_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ (completed_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Cross-Check</span>
                                    <span class="text-muted">{{ cross_check_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-secondary" role="progressbar" 
                                         style="width: {{ (cross_check_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <!-- System Status -->
                            <h6 class="text-muted mb-3">System Status</h6>
                            <div class="list-group list-group-flush">
                                {% for component, status in system_status.items() %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0 py-2 border-0">
                                    <span>{{ component.replace('_', ' ').title() }}</span>
                                    <span class="badge bg-{{ 'success' if status in ['Online', 'Connected', 'Accessible', 'Web Player Ready'] else 'warning' if status == 'Limited Access' else 'secondary' }}">
                                        {{ status }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Activity -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if current_user.role in ['executor_public', 'executor_private', 'main_admin'] %}
                        <button class="btn btn-outline-primary" onclick="showComingSoon('Create New Task')">
                            <i class="fas fa-plus me-2"></i>Create New Task
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-info" onclick="showComingSoon('File Browser')">
                            <i class="fas fa-folder-open me-2"></i>Browse Files
                        </button>
                        
                        <button class="btn btn-outline-success" onclick="showComingSoon('Media Player')">
                            <i class="fas fa-play me-2"></i>Open Media Player
                        </button>
                        
                        {% if current_user.role in ['cross_checker_public', 'cross_checker_private', 'main_admin'] %}
                        <button class="btn btn-outline-warning" onclick="showComingSoon('Cross-Check Queue')">
                            <i class="fas fa-check-double me-2"></i>Cross-Check Queue
                        </button>
                        {% endif %}
                        
                        {% if current_user.role == 'main_admin' %}
                        <hr>
                        <button class="btn btn-outline-secondary" onclick="showComingSoon('User Management')">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </button>
                        
                        <button class="btn btn-outline-secondary" onclick="showComingSoon('System Settings')">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </button>
                        {% endif %}
                    </div>
                    
                    <!-- Recent Activity -->
                    {% if recent_tasks %}
                    <hr>
                    <h6 class="text-muted mb-3">Recent Activity</h6>
                    <div class="list-group list-group-flush">
                        {% for task in recent_tasks[:3] %}
                        <div class="list-group-item px-0 py-2 border-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fs-6">{{ task.file_name[:30] }}{% if task.file_name|length > 30 %}...{% endif %}</h6>
                                    <p class="mb-1 small text-muted">{{ task.category.replace('_', ' ').title() }}</p>
                                    <small class="text-muted">{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <span class="badge bg-{{ 'warning' if task.status == 'pending' else 'info' if task.status == 'in_progress' else 'success' if task.status == 'completed' else 'secondary' }}">
                                    {{ task.status.replace('_', ' ').title() }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Role-Specific Quick Actions -->
    {% if current_user.role == 'main_admin' %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-primary border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-crown fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-2">Administrator Dashboard</h4>
                        <p class="mb-2">
                            You have full system access. Manage users, configure paths, oversee all tasks and cross-checks.
                        </p>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('users') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-users me-1"></i>Manage Users
                            </a>
                            <a href="{{ url_for('reports') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-bar me-1"></i>View Reports
                            </a>
                            <a href="{{ url_for('audit_logs') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-shield-alt me-1"></i>Audit Logs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif current_user.role in ['executor_public', 'executor_private'] %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-success border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-tasks fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-2">Executor Dashboard</h4>
                        <p class="mb-2">
                            Process {{ 'private and public' if current_user.role == 'executor_private' else 'public' }} video content.
                            {% if current_user.role == 'executor_private' %}Enhanced security protocols apply.{% endif %}
                        </p>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('tasks') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-list me-1"></i>My Tasks
                            </a>
                            <a href="{{ url_for('files') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-folder-open me-1"></i>Browse Files
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% elif current_user.role in ['cross_checker_public', 'cross_checker_private'] %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-warning border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-check-double fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-2">Cross-Checker Dashboard</h4>
                        <p class="mb-2">
                            Review and validate {{ 'private and public' if current_user.role == 'cross_checker_private' else 'public' }} content quality.
                            {% if current_user.role == 'cross_checker_private' %}Enhanced security protocols apply.{% endif %}
                        </p>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('cross_checks') }}" class="btn btn-warning btn-sm">
                                <i class="fas fa-clipboard-check me-1"></i>My Reviews
                            </a>
                            <a href="{{ url_for('reports') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-line me-1"></i>Quality Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-eye fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-2">Viewer Dashboard</h4>
                        <p class="mb-2">
                            Monitor system activities, view reports, and track quality metrics with read-only access.
                        </p>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ url_for('reports') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-chart-bar me-1"></i>View Reports
                            </a>
                            <a href="{{ url_for('tasks') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-list me-1"></i>View Tasks
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- System Information -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-light border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle fa-2x me-3 text-primary"></i>
                    <div>
                        <h6 class="alert-heading mb-2">Archives Assignment Console - Production Ready</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-check-circle text-success me-2"></i>Active Features:</h6>
                                <ul class="mb-0 small">
                                    <li>Complete role-based access control (6 roles)</li>
                                    <li>Two-factor authentication for private content</li>
                                    <li>File browser with video streaming</li>
                                    <li>Task management and assignment</li>
                                    <li>Cross-checking workflows</li>
                                    <li>Comprehensive audit logging</li>
                                    <li>Real-time reports and analytics</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-shield-alt text-success me-2"></i>Security Features:</h6>
                                <ul class="mb-0 small">
                                    <li>Secure password hashing</li>
                                    <li>Session management with timeouts</li>
                                    <li>Failed login protection</li>
                                    <li>File access controls</li>
                                    <li>Audit trail for all actions</li>
                                    <li>Role-based permissions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showComingSoon(feature) {
    alert(`${feature} functionality will be implemented in the next development phase.\n\nThe foundation is complete and ready for feature implementation!`);
}

// Auto-refresh dashboard stats every 30 seconds
setInterval(function() {
    // In a real implementation, this would make an AJAX call to update stats
    console.log('Dashboard auto-refresh (placeholder)');
}, 30000);
</script>
{% endblock %}
