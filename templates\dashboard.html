{% extends "base.html" %}

{% block title %}Dashboard - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="display-5 mb-1">
                        <i class="fas fa-tachometer-alt text-primary me-3"></i>
                        Dashboard
                    </h1>
                    <p class="text-muted mb-0">Welcome back, {{ current_user.full_name }} ({{ user_role_display }})</p>
                </div>
                <div class="text-end">
                    <div class="badge bg-success fs-6 mb-1">
                        <i class="fas fa-circle me-1"></i>Online
                    </div>
                    <div class="small text-muted">Port 5004</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-primary mb-1">{{ total_users }}</h3>
                            <p class="text-muted mb-0">Active Users</p>
                        </div>
                        <div class="text-primary">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-info mb-1">{{ total_tasks }}</h3>
                            <p class="text-muted mb-0">Total Tasks</p>
                        </div>
                        <div class="text-info">
                            <i class="fas fa-tasks fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-warning mb-1">{{ pending_tasks }}</h3>
                            <p class="text-muted mb-0">Pending Tasks</p>
                        </div>
                        <div class="text-warning">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="text-success mb-1">{{ completed_tasks }}</h3>
                            <p class="text-muted mb-0">Completed Tasks</p>
                        </div>
                        <div class="text-success">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Task Overview -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>
                        Task Status Overview
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Progress Bars -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Pending</span>
                                    <span class="text-muted">{{ pending_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" role="progressbar" 
                                         style="width: {{ (pending_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">In Progress</span>
                                    <span class="text-muted">{{ in_progress_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-info" role="progressbar" 
                                         style="width: {{ (in_progress_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Completed</span>
                                    <span class="text-muted">{{ completed_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" 
                                         style="width: {{ (completed_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="text-muted">Cross-Check</span>
                                    <span class="text-muted">{{ cross_check_tasks }}</span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-secondary" role="progressbar" 
                                         style="width: {{ (cross_check_tasks / (total_tasks if total_tasks > 0 else 1) * 100) }}%"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <!-- System Status -->
                            <h6 class="text-muted mb-3">System Status</h6>
                            <div class="list-group list-group-flush">
                                {% for component, status in system_status.items() %}
                                <div class="list-group-item d-flex justify-content-between align-items-center px-0 py-2 border-0">
                                    <span>{{ component.replace('_', ' ').title() }}</span>
                                    <span class="badge bg-{{ 'success' if status in ['Online', 'Connected', 'Accessible', 'Web Player Ready'] else 'warning' if status == 'Limited Access' else 'secondary' }}">
                                        {{ status }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & Recent Activity -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2 text-primary"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if current_user.role in ['executor_public', 'executor_private', 'main_admin'] %}
                        <button class="btn btn-outline-primary" onclick="showComingSoon('Create New Task')">
                            <i class="fas fa-plus me-2"></i>Create New Task
                        </button>
                        {% endif %}
                        
                        <button class="btn btn-outline-info" onclick="showComingSoon('File Browser')">
                            <i class="fas fa-folder-open me-2"></i>Browse Files
                        </button>
                        
                        <button class="btn btn-outline-success" onclick="showComingSoon('Media Player')">
                            <i class="fas fa-play me-2"></i>Open Media Player
                        </button>
                        
                        {% if current_user.role in ['cross_checker_public', 'cross_checker_private', 'main_admin'] %}
                        <button class="btn btn-outline-warning" onclick="showComingSoon('Cross-Check Queue')">
                            <i class="fas fa-check-double me-2"></i>Cross-Check Queue
                        </button>
                        {% endif %}
                        
                        {% if current_user.role == 'main_admin' %}
                        <hr>
                        <button class="btn btn-outline-secondary" onclick="showComingSoon('User Management')">
                            <i class="fas fa-users me-2"></i>Manage Users
                        </button>
                        
                        <button class="btn btn-outline-secondary" onclick="showComingSoon('System Settings')">
                            <i class="fas fa-cogs me-2"></i>System Settings
                        </button>
                        {% endif %}
                    </div>
                    
                    <!-- Recent Activity -->
                    {% if recent_tasks %}
                    <hr>
                    <h6 class="text-muted mb-3">Recent Activity</h6>
                    <div class="list-group list-group-flush">
                        {% for task in recent_tasks[:3] %}
                        <div class="list-group-item px-0 py-2 border-0">
                            <div class="d-flex justify-content-between align-items-start">
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fs-6">{{ task.file_name[:30] }}{% if task.file_name|length > 30 %}...{% endif %}</h6>
                                    <p class="mb-1 small text-muted">{{ task.category.replace('_', ' ').title() }}</p>
                                    <small class="text-muted">{{ task.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                </div>
                                <span class="badge bg-{{ 'warning' if task.status == 'pending' else 'info' if task.status == 'in_progress' else 'success' if task.status == 'completed' else 'secondary' }}">
                                    {{ task.status.replace('_', ' ').title() }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Welcome Message -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info border-0 shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    <i class="fas fa-rocket fa-2x me-3"></i>
                    <div>
                        <h4 class="alert-heading mb-2">Welcome to Archives Assignment Console!</h4>
                        <p class="mb-2">
                            This Flask-based web application is now running on <strong>port 5004</strong> with full functionality for video file organization and processing workflows.
                        </p>
                        <hr>
                        <div class="row">
                            <div class="col-md-6">
                                <h6><i class="fas fa-check-circle text-success me-2"></i>Features Available:</h6>
                                <ul class="mb-0">
                                    <li>Role-based access control</li>
                                    <li>User authentication & session management</li>
                                    <li>Dashboard with real-time statistics</li>
                                    <li>SQLite database with audit logging</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="fas fa-tools text-warning me-2"></i>Coming Soon:</h6>
                                <ul class="mb-0">
                                    <li>File browser & media player</li>
                                    <li>Task management & assignment</li>
                                    <li>Cross-checking workflows</li>
                                    <li>Advanced reporting & analytics</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function showComingSoon(feature) {
    alert(`${feature} functionality will be implemented in the next development phase.\n\nThe foundation is complete and ready for feature implementation!`);
}

// Auto-refresh dashboard stats every 30 seconds
setInterval(function() {
    // In a real implementation, this would make an AJAX call to update stats
    console.log('Dashboard auto-refresh (placeholder)');
}, 30000);
</script>
{% endblock %}
