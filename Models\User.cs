using System.ComponentModel.DataAnnotations;

namespace ArchivesAssignmentConsole.Models
{
    public class User
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;
        
        [Required]
        public string PasswordHash { get; set; } = string.Empty;
        
        [Required]
        [StringLength(100)]
        public string FullName { get; set; } = string.Empty;
        
        [Required]
        [EmailAddress]
        [StringLength(100)]
        public string Email { get; set; } = string.Empty;
        
        public UserRole Role { get; set; }
        
        public bool IsActive { get; set; } = true;
        
        public bool RequiresTwoFactor { get; set; }
        
        public string? TwoFactorSecret { get; set; }
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? LastLoginAt { get; set; }
        
        public int FailedLoginAttempts { get; set; }
        
        public DateTime? LockedUntil { get; set; }
        
        // Navigation properties
        public virtual ICollection<FileTask> AssignedTasks { get; set; } = new List<FileTask>();
        public virtual ICollection<CrossCheck> CrossChecksAssigned { get; set; } = new List<CrossCheck>();
        public virtual ICollection<CrossCheck> CrossChecksPerformed { get; set; } = new List<CrossCheck>();
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
    }

    public enum UserRole
    {
        MainAdmin = 1,          // Super Administrator and Assignment Coordinator
        ExecutorPublic = 2,     // Public Content Processor
        ExecutorPrivate = 3,    // Private Content Processor
        CrossCheckerPublic = 4, // Public Content Quality Assurance
        CrossCheckerPrivate = 5,// Private Content Quality Assurance
        Viewer = 6              // Read-Only Observer
    }
}
