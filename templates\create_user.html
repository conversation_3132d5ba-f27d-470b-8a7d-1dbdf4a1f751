{% extends "base.html" %}

{% block title %}Create User - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-user-plus text-primary me-3"></i>
                    Create New User
                </h1>
                <a href="{{ url_for('users') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-cog me-2"></i>
                        User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">
                                        <i class="fas fa-user me-1"></i>Username *
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <div class="form-text">Must be unique. Used for login.</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">
                                        <i class="fas fa-envelope me-1"></i>Email Address *
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                    <div class="form-text">Must be unique and valid.</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="full_name" class="form-label">
                                <i class="fas fa-id-card me-1"></i>Full Name *
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">
                                        <i class="fas fa-user-tag me-1"></i>User Role *
                                    </label>
                                    <select class="form-select" id="role" name="role" required onchange="updateRoleInfo()">
                                        <option value="">Select Role</option>
                                        <option value="main_admin">Main Admin/Assigner</option>
                                        <option value="executor_public">Executor Public</option>
                                        <option value="executor_private">Executor Private</option>
                                        <option value="cross_checker_public">Cross-Checker Public</option>
                                        <option value="cross_checker_private">Cross-Checker Private</option>
                                        <option value="viewer">Viewer</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>Password *
                                    </label>
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <div class="form-text">Minimum 8 characters recommended.</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="requires_2fa" name="requires_2fa">
                                <label class="form-check-label" for="requires_2fa">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    Require Two-Factor Authentication
                                </label>
                                <div class="form-text">Recommended for private content access.</div>
                            </div>
                        </div>

                        <!-- Role Information Panel -->
                        <div id="roleInfo" class="alert alert-info d-none">
                            <h6><i class="fas fa-info-circle me-2"></i>Role Information</h6>
                            <div id="roleDescription"></div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ url_for('users') }}" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
const roleDescriptions = {
    'main_admin': {
        title: 'Main Admin/Assigner',
        description: 'Full system access including user management, path configuration, task assignment, and cross-checking oversight. Can access all content types.',
        permissions: ['User Management', 'System Configuration', 'All Content Access', 'Reports & Analytics'],
        color: 'primary'
    },
    'executor_public': {
        title: 'Executor Public',
        description: 'Process non-sensitive video content in public categories. Can move/copy files and submit work for cross-checking.',
        permissions: ['Public Content Processing', 'File Operations', 'Task Management', 'VLC Preview'],
        color: 'info'
    },
    'executor_private': {
        title: 'Executor Private',
        description: 'Process sensitive video content with enhanced security. Access to all categories including private ones. Mandatory cross-checking.',
        permissions: ['All Content Processing', 'Private Content Access', 'Enhanced Security', 'Priority Queue'],
        color: 'danger'
    },
    'cross_checker_public': {
        title: 'Cross-Checker Public',
        description: 'Validate quality and accuracy of public content processing. Provide feedback and generate quality reports.',
        permissions: ['Public Content Review', 'Quality Assessment', 'Feedback System', 'Report Generation'],
        color: 'warning'
    },
    'cross_checker_private': {
        title: 'Cross-Checker Private',
        description: 'Validate quality and security of private content with enhanced security protocols. Requires 2FA.',
        permissions: ['All Content Review', 'Security Validation', 'Compliance Checking', 'Audit Documentation'],
        color: 'warning'
    },
    'viewer': {
        title: 'Viewer',
        description: 'Read-only access to monitor system activities, view statistics, and generate reports. No content modification.',
        permissions: ['Dashboard Access', 'Statistics Viewing', 'Report Generation', 'System Monitoring'],
        color: 'secondary'
    }
};

function updateRoleInfo() {
    const roleSelect = document.getElementById('role');
    const roleInfo = document.getElementById('roleInfo');
    const roleDescription = document.getElementById('roleDescription');
    const requires2fa = document.getElementById('requires_2fa');
    
    const selectedRole = roleSelect.value;
    
    if (selectedRole && roleDescriptions[selectedRole]) {
        const role = roleDescriptions[selectedRole];
        
        roleDescription.innerHTML = `
            <h6 class="text-${role.color}">${role.title}</h6>
            <p class="mb-2">${role.description}</p>
            <h6>Permissions:</h6>
            <ul class="mb-0">
                ${role.permissions.map(perm => `<li>${perm}</li>`).join('')}
            </ul>
        `;
        
        roleInfo.classList.remove('d-none');
        roleInfo.className = `alert alert-${role.color}`;
        
        // Auto-enable 2FA for private roles
        if (selectedRole.includes('private')) {
            requires2fa.checked = true;
        }
    } else {
        roleInfo.classList.add('d-none');
    }
}

// Form validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    if (password.length < 8) {
        e.preventDefault();
        alert('Password must be at least 8 characters long.');
        return false;
    }
});
</script>
{% endblock %}
