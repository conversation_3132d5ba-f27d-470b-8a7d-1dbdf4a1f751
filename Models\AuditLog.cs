using System.ComponentModel.DataAnnotations;

namespace ArchivesAssignmentConsole.Models
{
    public class AuditLog
    {
        public int Id { get; set; }
        
        public int UserId { get; set; }
        public virtual User User { get; set; } = null!;
        
        public AuditAction Action { get; set; }
        
        [Required]
        [StringLength(100)]
        public string EntityType { get; set; } = string.Empty;
        
        public int? EntityId { get; set; }
        
        [StringLength(500)]
        public string? EntityName { get; set; }
        
        [StringLength(2000)]
        public string? Details { get; set; }
        
        [StringLength(45)]
        public string? IpAddress { get; set; }
        
        [StringLength(500)]
        public string? UserAgent { get; set; }
        
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        
        public bool IsSecurityEvent { get; set; }
        
        public int? FileTaskId { get; set; }
        public virtual FileTask? FileTask { get; set; }
        
        public int? CrossCheckId { get; set; }
        public virtual CrossCheck? CrossCheck { get; set; }
    }

    public enum AuditAction
    {
        Login = 1,
        Logout = 2,
        LoginFailed = 3,
        PasswordChanged = 4,
        UserCreated = 5,
        UserModified = 6,
        UserDeleted = 7,
        UserLocked = 8,
        UserUnlocked = 9,
        FileTaskCreated = 10,
        FileTaskAssigned = 11,
        FileTaskStarted = 12,
        FileTaskCompleted = 13,
        FileTaskModified = 14,
        FileTaskDeleted = 15,
        FileMoved = 16,
        FileCopied = 17,
        FileDeleted = 18,
        FileViewed = 19,
        CrossCheckCreated = 20,
        CrossCheckAssigned = 21,
        CrossCheckStarted = 22,
        CrossCheckCompleted = 23,
        CrossCheckModified = 24,
        SettingsChanged = 25,
        PathConfigurationChanged = 26,
        SecurityEvent = 27,
        TwoFactorEnabled = 28,
        TwoFactorDisabled = 29,
        PrivateContentAccessed = 30,
        VlcPlaybackStarted = 31,
        VlcPlaybackStopped = 32,
        ReportGenerated = 33,
        DataExported = 34,
        SystemBackup = 35,
        SystemRestore = 36
    }
}
