namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IEncryptionService
    {
        string Encrypt(string plainText);
        string Decrypt(string cipherText);
        byte[] Encrypt(byte[] data);
        byte[] Decrypt(byte[] encryptedData);
        string HashPassword(string password);
        bool VerifyPassword(string password, string hash);
        string GenerateSecureToken(int length = 32);
        string GenerateTwoFactorSecret();
        bool ValidateTwoFactorCode(string secret, string code);
        string EncryptFile(string filePath, string outputPath);
        string DecryptFile(string encryptedFilePath, string outputPath);
        bool VerifyFileIntegrity(string filePath, string expectedHash);
        string CalculateFileHash(string filePath);
    }
}
