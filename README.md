# Archives Assignment Console

A comprehensive video file organization and processing workflow system with both desktop and web interfaces, featuring embedded VLC media player, role-based access control, and integrated cross-checking mechanisms.

## Features

### 🖥️ **Dual Interface Support**
- **Desktop WPF Application**: Full-featured native Windows app with embedded VLC player
- **Web Interface**: Browser-based access running on port 5004 for remote users

### 🎬 **Embedded VLC Media Player**
- Standalone VLC integration independent of system installation
- Secure playback with audit logging for private content
- Standard playback controls (play, pause, stop, seek)
- Support for multiple video formats

### 👥 **Role-Based Access Control**
- **Main Admin/Assigner**: Super administrator with full system access
- **Executor Public**: Public content processor
- **Executor Private**: Private content processor with enhanced security
- **Cross-Checker Public**: Public content quality assurance
- **Cross-Checker Private**: Private content quality assurance with 2FA
- **Viewer**: Read-only observer with monitoring capabilities

### 📁 **File Management System**
- Dynamic folder browsing and path configuration
- Batch file operations
- Automated file integrity checking
- Configurable destination categories

### ✅ **Cross-Checking Workflows**
- Quality assurance processes for all content
- Mandatory cross-checking for private content
- Dual cross-checking for critical items
- Feedback and revision tracking

### 🔒 **Security Features**
- Two-factor authentication for private content access
- Encryption for sensitive data
- Comprehensive audit logging
- Session management and lockout protection

## Prerequisites

### Required Software
1. **.NET 8.0 SDK** - Download from [https://dotnet.microsoft.com/download/dotnet/8.0](https://dotnet.microsoft.com/download/dotnet/8.0)
2. **Visual Studio 2022** or **Visual Studio Code** (recommended for development)
3. **Windows 10/11** (for desktop application)

### Default Folder Structure
The application expects the following default paths (configurable):
```
T:\To_Process\Rough folder\
├── To Process\restore Files\          (Source)
└── To Check\
    ├── Miscellaneous\                 (Destination)
    ├── Private one video\             (Private Destination)
    └── Private Two\                   (Private Destination)
```

## Quick Start

### 1. Install .NET 8.0 SDK
If not already installed, download and install the .NET 8.0 SDK from Microsoft.

### 2. Build the Solution
```bash
# Navigate to project directory
cd "Archives Backlog Project"

# Restore packages and build
dotnet restore
dotnet build
```

### 3. Run the Web Interface (Port 5004)
```bash
# Start the web application
cd ArchivesAssignmentConsole.Web
dotnet run

# Or use the startup script
.\start-web.bat
```
Access the web interface at: **http://localhost:5004**

### 4. Run the Desktop Application
```bash
# Start the desktop application
cd ArchivesAssignmentConsole.Desktop
dotnet run

# Or use the startup script
.\start-desktop.bat
```

### 5. Default Login Credentials
- **Username**: `admin`
- **Password**: `admin123`
- **Role**: Main Admin/Assigner

## Project Structure

```
Archives Assignment Console/
├── ArchivesAssignmentConsole.Core/          # Shared business logic and models
│   ├── Models/                              # Data models
│   ├── Interfaces/                          # Service interfaces
│   ├── Data/                               # Database context
│   └── Services/                           # Core services
├── ArchivesAssignmentConsole.Web/           # Web interface (Port 5004)
│   ├── Controllers/                         # MVC controllers
│   ├── Views/                              # Razor views
│   ├── wwwroot/                            # Static files
│   └── Services/                           # Web-specific services
├── ArchivesAssignmentConsole.Desktop/       # WPF desktop application
│   ├── Views/                              # WPF windows and user controls
│   ├── ViewModels/                         # MVVM view models
│   ├── Services/                           # Desktop-specific services
│   └── Controls/                           # Custom WPF controls
└── README.md                               # This file
```

## Configuration

### Database
- **Type**: SQLite (archives.db)
- **Location**: Application root directory
- **Auto-created**: Yes, on first run

### Paths Configuration
Edit `appsettings.json` in each project to configure default paths:
```json
{
  "DefaultPaths": {
    "SourcePath": "T:\\To_Process\\Rough folder\\To Process\\restore Files",
    "Destinations": {
      "Miscellaneous": "T:\\To_Process\\Rough folder\\To Check\\Miscellaneous",
      "PrivateOne": "T:\\To_Process\\Rough folder\\To Check\\Private one video",
      "PrivateTwo": "T:\\To_Process\\Rough folder\\To Check\\Private Two",
      "ToBeProcessed": "T:\\To_Process\\Rough folder\\To Process\\restore Files"
    }
  }
}
```

### Security Settings
Configure security options in `appsettings.json`:
```json
{
  "Security": {
    "RequireTwoFactorForPrivate": true,
    "SessionTimeoutMinutes": 60,
    "MaxLoginAttempts": 3,
    "LockoutDurationMinutes": 15
  }
}
```

## User Roles and Permissions

### Main Admin/Assigner
- Full system configuration and management
- User account creation and role assignment
- Path configuration and workflow management
- Cross-checking oversight and reporting
- Database backup and restore

### Executor Public
- Process non-sensitive video content
- Access public destination categories
- Submit work for cross-checking
- Update processing status

### Executor Private
- Process sensitive video content with enhanced security
- Access all destination categories including private ones
- Mandatory cross-checking for all work
- Detailed audit logging

### Cross-Checker Public
- Validate quality of public content processing
- Provide feedback and generate reports
- Read-only file access with annotation capabilities

### Cross-Checker Private
- Validate quality and security of private content
- Enhanced security with mandatory 2FA
- Encrypted feedback and secure reporting
- Comprehensive audit documentation

### Viewer
- Read-only access to dashboards and statistics
- Report generation and data export
- System monitoring capabilities

## Development

### Adding New Features
1. Add models to `ArchivesAssignmentConsole.Core/Models/`
2. Create service interfaces in `ArchivesAssignmentConsole.Core/Interfaces/`
3. Implement services in respective projects
4. Update database context if needed
5. Create UI components (Web: Controllers/Views, Desktop: Views/ViewModels)

### Database Migrations
```bash
# Add migration
dotnet ef migrations add MigrationName --project ArchivesAssignmentConsole.Core

# Update database
dotnet ef database update --project ArchivesAssignmentConsole.Core
```

## Troubleshooting

### Common Issues

1. **Port 5004 already in use**
   - Change port in `Program.cs` or stop conflicting service
   - Check `netstat -an | findstr :5004`

2. **VLC not working**
   - Ensure VideoLAN.LibVLC.Windows package is installed
   - Check VLC libraries are copied to output directory

3. **Database connection issues**
   - Verify SQLite file permissions
   - Check connection string in appsettings.json

4. **Path access denied**
   - Ensure application has read/write access to configured paths
   - Run as administrator if necessary

### Logs
- **Desktop**: `logs/desktop-YYYY-MM-DD.txt`
- **Web**: `logs/web-YYYY-MM-DD.txt`
- **Database**: SQLite database file `archives.db`

## Support

For issues and feature requests, please check the logs first and ensure all prerequisites are met. The application includes comprehensive logging and audit trails to help diagnose problems.

## License

Copyright © 2025 Archives Management System. All rights reserved.
