{% extends "base.html" %}

{% block title %}Two-Factor Authentication - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-4">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-warning text-dark text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Two-Factor Authentication
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="text-center mb-4">
                        <i class="fas fa-mobile-alt fa-3x text-warning mb-3"></i>
                        <h5>Security Verification Required</h5>
                        <p class="text-muted">
                            Please enter the 6-digit code from your authenticator app to continue.
                        </p>
                    </div>

                    <form method="POST">
                        <div class="mb-4">
                            <label for="code" class="form-label text-center d-block">Verification Code</label>
                            <input type="text" class="form-control form-control-lg text-center" 
                                   id="code" name="code" placeholder="000000" 
                                   maxlength="6" pattern="[0-9]{6}" required
                                   style="font-size: 2rem; letter-spacing: 0.5rem;">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-warning btn-lg">
                                <i class="fas fa-unlock me-2"></i>Verify
                            </button>
                            <a href="{{ url_for('logout') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center bg-light">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        This is required for private content access
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-focus on code input
document.getElementById('code').focus();

// Auto-submit when 6 digits are entered
document.getElementById('code').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        setTimeout(() => {
            e.target.form.submit();
        }, 500);
    }
});

// Only allow numbers
document.getElementById('code').addEventListener('keypress', function(e) {
    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
