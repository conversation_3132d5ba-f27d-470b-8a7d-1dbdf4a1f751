@echo off
echo Building Archives Assignment Console Solution...
echo.

echo Restoring NuGet packages...
dotnet restore

echo.
echo Building Core project...
dotnet build ArchivesAssignmentConsole.Core --configuration Release

echo.
echo Building Web project...
dotnet build ArchivesAssignmentConsole.Web --configuration Release

echo.
echo Building Desktop project...
dotnet build ArchivesAssignmentConsole.Desktop --configuration Release

echo.
echo Build completed successfully!
echo.
echo To run the applications:
echo   Web Interface:     start-web.bat
echo   Desktop App:       start-desktop.bat
echo.

pause
