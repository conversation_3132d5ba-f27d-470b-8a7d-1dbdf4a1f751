{% extends "base.html" %}

{% block title %}Setup Two-Factor Authentication - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Setup Two-Factor Authentication
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Enhanced Security</h6>
                        <p class="mb-0">Two-factor authentication adds an extra layer of security to your account, especially important for private content access.</p>
                    </div>

                    <div class="text-center mb-4">
                        <h5>Step 1: Scan QR Code</h5>
                        <p class="text-muted">Use your authenticator app to scan this QR code:</p>
                        <div class="qr-code-container mb-3">
                            <img src="data:image/png;base64,{{ qr_code }}" alt="QR Code" class="img-fluid border rounded">
                        </div>
                        <p class="small text-muted">
                            Recommended apps: Google Authenticator, Microsoft Authenticator, Authy
                        </p>
                    </div>

                    <div class="text-center mb-4">
                        <h5>Step 2: Manual Entry (Alternative)</h5>
                        <p class="text-muted">If you can't scan the QR code, enter this secret manually:</p>
                        <div class="alert alert-secondary">
                            <code class="fs-6">{{ secret }}</code>
                        </div>
                    </div>

                    <form action="{{ url_for('verify_2fa_setup') }}" method="POST">
                        <div class="text-center">
                            <h5>Step 3: Verify Setup</h5>
                            <p class="text-muted">Enter the 6-digit code from your authenticator app:</p>
                            
                            <div class="mb-3">
                                <input type="text" class="form-control form-control-lg text-center" 
                                       id="code" name="code" placeholder="000000" 
                                       maxlength="6" pattern="[0-9]{6}" required
                                       style="font-size: 2rem; letter-spacing: 0.5rem;">
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-check me-2"></i>Verify and Enable 2FA
                                </button>
                                <a href="{{ url_for('profile') }}" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Tips -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Security Tips
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="mb-0">
                        <li>Keep your authenticator app secure and backed up</li>
                        <li>Don't share your secret key with anyone</li>
                        <li>Save backup codes in a secure location</li>
                        <li>Use a different device for your authenticator if possible</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-focus on code input
document.getElementById('code').focus();

// Auto-submit when 6 digits are entered
document.getElementById('code').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        // Small delay to show the complete code
        setTimeout(() => {
            e.target.form.submit();
        }, 500);
    }
});

// Only allow numbers
document.getElementById('code').addEventListener('keypress', function(e) {
    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
