using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface ICrossCheckService
    {
        Task<CrossCheck> CreateCrossCheckAsync(int fileTaskId, int assignedToUserId, bool requiresSecondReview = false);
        Task<CrossCheck?> GetCrossCheckByIdAsync(int id);
        Task<IEnumerable<CrossCheck>> GetCrossChecksForUserAsync(int userId, CrossCheckStatus? status = null);
        Task<IEnumerable<CrossCheck>> GetCrossChecksForFileTaskAsync(int fileTaskId);
        Task<IEnumerable<CrossCheck>> GetPendingCrossChecksAsync();
        Task<CrossCheck> AssignCrossCheckAsync(int crossCheckId, int assignedToUserId);
        Task<CrossCheck> StartCrossCheckAsync(int crossCheckId, int reviewedByUserId);
        Task<CrossCheck> CompleteCrossCheckAsync(int crossCheckId, CrossCheckResult result, string? reviewNotes = null, string? feedback = null, int? qualityScore = null);
        Task<CrossCheck> CreateSecondReviewAsync(int firstReviewId, int assignedToUserId);
        Task<IEnumerable<CrossCheck>> GetCrossChecksRequiringSecondReviewAsync();
        Task<Dictionary<CrossCheckStatus, int>> GetCrossCheckCountByStatusAsync();
        Task<Dictionary<CrossCheckResult, int>> GetCrossCheckResultSummaryAsync(DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<User>> GetAvailableCrossCheckersAsync(bool isPrivateContent);
        Task<bool> AutoAssignCrossCheckAsync(int fileTaskId, bool isPrivateContent);
        Task<double> GetAverageQualityScoreAsync(int? userId = null, DateTime? fromDate = null, DateTime? toDate = null);
        Task<IEnumerable<CrossCheck>> GetCrossChecksByQualityScoreAsync(int minScore, int maxScore);
        Task<bool> EscalateCrossCheckAsync(int crossCheckId, string reason);
    }
}
