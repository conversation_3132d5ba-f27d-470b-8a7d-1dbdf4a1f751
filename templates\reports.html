{% extends "base.html" %}

{% block title %}Reports & Analytics - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-chart-bar text-primary me-3"></i>
                    Reports & Analytics
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshReports()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button type="button" class="btn btn-primary" onclick="exportReport()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-tasks fa-2x text-primary mb-2"></i>
                    <h3 class="text-primary">{{ total_tasks }}</h3>
                    <p class="mb-0">Total Tasks</p>
                    <small class="text-muted">All time</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ completed_tasks }}</h3>
                    <p class="mb-0">Completed</p>
                    <small class="text-muted">{{ ((completed_tasks / total_tasks * 100) if total_tasks > 0 else 0) | round(1) }}% completion rate</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-double fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ total_cross_checks }}</h3>
                    <p class="mb-0">Cross-Checks</p>
                    <small class="text-muted">{{ ((completed_cross_checks / total_cross_checks * 100) if total_cross_checks > 0 else 0) | round(1) }}% completed</small>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-star fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ avg_quality_score }}</h3>
                    <p class="mb-0">Avg Quality</p>
                    <small class="text-muted">Out of 10</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Task Status Chart -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Task Status Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="taskStatusChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>

        <!-- Quality Trends -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Quality Score Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="qualityTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Tasks -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Recent Tasks
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for task in recent_tasks %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ task.file_name[:30] }}{% if task.file_name|length > 30 %}...{% endif %}</h6>
                                    <small class="text-muted">
                                        {{ task.category.replace('_', ' ').title() }} • 
                                        {{ task.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                <span class="badge bg-{{ 'warning' if task.status == 'pending' else 'info' if task.status == 'in_progress' else 'success' if task.status in ['completed', 'approved'] else 'secondary' }}">
                                    {{ task.status.replace('_', ' ').title() }}
                                </span>
                            </div>
                        </div>
                        {% endfor %}
                        {% if not recent_tasks %}
                        <div class="list-group-item text-center py-4">
                            <i class="fas fa-inbox text-muted fa-2x mb-2"></i>
                            <p class="text-muted mb-0">No recent tasks</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Cross-Checks -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-check-double me-2"></i>
                        Recent Cross-Checks
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% for cross_check in recent_cross_checks %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">{{ cross_check.file_task.file_name[:30] }}{% if cross_check.file_task.file_name|length > 30 %}...{% endif %}</h6>
                                    <small class="text-muted">
                                        {% if cross_check.reviewed_by %}{{ cross_check.reviewed_by.full_name }}{% else %}{{ cross_check.assigned_user.full_name }}{% endif %} • 
                                        {{ cross_check.created_at.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ 'warning' if cross_check.status == 'pending' else 'info' if cross_check.status == 'assigned' else 'success' if cross_check.status == 'completed' else 'secondary' }}">
                                        {{ cross_check.status.replace('_', ' ').title() }}
                                    </span>
                                    {% if cross_check.quality_score %}
                                    <br><small class="text-muted">Quality: {{ cross_check.quality_score }}/10</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        {% if not recent_cross_checks %}
                        <div class="list-group-item text-center py-4">
                            <i class="fas fa-inbox text-muted fa-2x mb-2"></i>
                            <p class="text-muted mb-0">No recent cross-checks</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Performance -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users me-2"></i>
                        User Performance Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <h4 class="text-primary">{{ active_users }}</h4>
                            <p class="mb-0">Active Users</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-success">{{ completed_tasks }}</h4>
                            <p class="mb-0">Tasks Completed</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-warning">{{ completed_cross_checks }}</h4>
                            <p class="mb-0">Reviews Completed</p>
                        </div>
                        <div class="col-md-3 text-center">
                            <h4 class="text-info">{{ avg_quality_score }}</h4>
                            <p class="mb-0">Average Quality</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Task Status Chart
const taskStatusCtx = document.getElementById('taskStatusChart').getContext('2d');
new Chart(taskStatusCtx, {
    type: 'doughnut',
    data: {
        labels: ['Pending', 'In Progress', 'Completed', 'Cross-Check'],
        datasets: [{
            data: [{{ pending_tasks }}, {{ in_progress_tasks }}, {{ completed_tasks }}, {{ total_cross_checks }}],
            backgroundColor: [
                '#ffc107',
                '#17a2b8',
                '#28a745',
                '#6c757d'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// Quality Trend Chart (placeholder data)
const qualityTrendCtx = document.getElementById('qualityTrendChart').getContext('2d');
new Chart(qualityTrendCtx, {
    type: 'line',
    data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
        datasets: [{
            label: 'Average Quality Score',
            data: [7.5, 8.2, 8.8, {{ avg_quality_score }}],
            borderColor: '#17a2b8',
            backgroundColor: 'rgba(23, 162, 184, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                max: 10
            }
        }
    }
});

function refreshReports() {
    window.location.reload();
}

function exportReport() {
    // Create CSV data
    const csvData = [
        ['Metric', 'Value'],
        ['Total Tasks', '{{ total_tasks }}'],
        ['Completed Tasks', '{{ completed_tasks }}'],
        ['Pending Tasks', '{{ pending_tasks }}'],
        ['In Progress Tasks', '{{ in_progress_tasks }}'],
        ['Total Cross-Checks', '{{ total_cross_checks }}'],
        ['Completed Cross-Checks', '{{ completed_cross_checks }}'],
        ['Average Quality Score', '{{ avg_quality_score }}'],
        ['Active Users', '{{ active_users }}']
    ];
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'archives_report_' + new Date().toISOString().split('T')[0] + '.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    
    ArchivesConsole.showNotification('Report exported successfully!', 'success');
}
</script>
{% endblock %}
