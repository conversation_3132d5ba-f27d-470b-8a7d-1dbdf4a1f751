using System.ComponentModel.DataAnnotations;

namespace ArchivesAssignmentConsole.Models
{
    public class FileTask
    {
        public int Id { get; set; }
        
        [Required]
        [StringLength(500)]
        public string SourcePath { get; set; } = string.Empty;
        
        [Required]
        [StringLength(500)]
        public string FileName { get; set; } = string.Empty;
        
        public long FileSize { get; set; }
        
        public string? FileHash { get; set; }
        
        public DestinationCategory Category { get; set; }
        
        [StringLength(500)]
        public string? DestinationPath { get; set; }
        
        public TaskStatus Status { get; set; } = TaskStatus.Pending;
        
        public TaskPriority Priority { get; set; } = TaskPriority.Normal;
        
        public bool IsPrivateContent { get; set; }
        
        public int? AssignedToUserId { get; set; }
        public virtual User? AssignedToUser { get; set; }
        
        public int CreatedByUserId { get; set; }
        public virtual User CreatedByUser { get; set; } = null!;
        
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        
        public DateTime? StartedAt { get; set; }
        
        public DateTime? CompletedAt { get; set; }
        
        [StringLength(1000)]
        public string? Notes { get; set; }
        
        [StringLength(1000)]
        public string? ProcessingNotes { get; set; }
        
        // Navigation properties
        public virtual ICollection<CrossCheck> CrossChecks { get; set; } = new List<CrossCheck>();
        public virtual ICollection<AuditLog> AuditLogs { get; set; } = new List<AuditLog>();
    }

    public enum DestinationCategory
    {
        Miscellaneous = 1,
        PrivateOne = 2,
        PrivateTwo = 3,
        ToBeProcessed = 4
    }

    public enum TaskStatus
    {
        Pending = 1,
        Assigned = 2,
        InProgress = 3,
        Completed = 4,
        CrossCheckRequired = 5,
        CrossCheckInProgress = 6,
        CrossCheckFailed = 7,
        Approved = 8,
        Rejected = 9,
        OnHold = 10
    }

    public enum TaskPriority
    {
        Low = 1,
        Normal = 2,
        High = 3,
        Critical = 4
    }
}
