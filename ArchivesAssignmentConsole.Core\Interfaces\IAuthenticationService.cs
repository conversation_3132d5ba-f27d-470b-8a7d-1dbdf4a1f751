using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IAuthenticationService
    {
        Task<AuthenticationResult> AuthenticateAsync(string username, string password, string? twoFactorCode = null);
        Task<bool> ValidateSessionAsync(string sessionToken);
        Task<User?> GetCurrentUserAsync();
        Task LogoutAsync();
        Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword);
        Task<string> GenerateTwoFactorSecretAsync(int userId);
        Task<bool> EnableTwoFactorAsync(int userId, string secret, string verificationCode);
        Task<bool> DisableTwoFactorAsync(int userId, string password);
        Task<bool> ValidateTwoFactorCodeAsync(string secret, string code);
        Task LockUserAsync(int userId, TimeSpan lockDuration);
        Task UnlockUserAsync(int userId);
        Task<bool> IsUserLockedAsync(int userId);
        Task RecordFailedLoginAttemptAsync(string username);
        Task ResetFailedLoginAttemptsAsync(int userId);
    }

    public class AuthenticationResult
    {
        public bool IsSuccess { get; set; }
        public User? User { get; set; }
        public string? SessionToken { get; set; }
        public string? ErrorMessage { get; set; }
        public bool RequiresTwoFactor { get; set; }
        public bool IsLocked { get; set; }
        public DateTime? LockedUntil { get; set; }
    }
}
