@echo off
echo.
echo ========================================
echo  Archives Assignment Console
echo  Flask Web Application - Port 5004
echo ========================================
echo.

echo Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.7+ from:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

echo Installing requirements...
pip install -r requirements.txt
if errorlevel 1 (
    echo.
    echo ❌ Failed to install requirements
    echo Try running: pip install Flask Flask-SQLAlchemy Flask-Login
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Requirements installed
echo.
echo 🚀 Starting Archives Assignment Console...
echo.
echo 📍 Web Interface: http://localhost:5004
echo 👤 Default Login: admin / admin123
echo 🛑 Press Ctrl+C to stop the server
echo.

python app.py

pause
