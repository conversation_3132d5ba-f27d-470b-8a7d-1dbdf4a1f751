using Microsoft.EntityFrameworkCore;
using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Data
{
    public class ArchivesDbContext : DbContext
    {
        public ArchivesDbContext(DbContextOptions<ArchivesDbContext> options) : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<FileTask> FileTasks { get; set; }
        public DbSet<CrossCheck> CrossChecks { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }
        public DbSet<PathConfiguration> PathConfigurations { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // User entity configuration
            modelBuilder.Entity<User>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Username).IsUnique();
                entity.HasIndex(e => e.Email).IsUnique();
                entity.Property(e => e.Role).HasConversion<int>();
            });

            // FileTask entity configuration
            modelBuilder.Entity<FileTask>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.FileName);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedAt);
                entity.Property(e => e.Category).HasConversion<int>();
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Priority).HasConversion<int>();

                entity.HasOne(e => e.AssignedToUser)
                    .WithMany(u => u.AssignedTasks)
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);
            });

            // CrossCheck entity configuration
            modelBuilder.Entity<CrossCheck>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Status);
                entity.HasIndex(e => e.CreatedAt);
                entity.Property(e => e.Status).HasConversion<int>();
                entity.Property(e => e.Result).HasConversion<int>();

                entity.HasOne(e => e.FileTask)
                    .WithMany(ft => ft.CrossChecks)
                    .HasForeignKey(e => e.FileTaskId)
                    .OnDelete(DeleteBehavior.Cascade);

                entity.HasOne(e => e.AssignedToUser)
                    .WithMany(u => u.CrossChecksAssigned)
                    .HasForeignKey(e => e.AssignedToUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ReviewedByUser)
                    .WithMany(u => u.CrossChecksPerformed)
                    .HasForeignKey(e => e.ReviewedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.FirstReview)
                    .WithMany(cc => cc.SecondReviews)
                    .HasForeignKey(e => e.FirstReviewId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // AuditLog entity configuration
            modelBuilder.Entity<AuditLog>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Timestamp);
                entity.HasIndex(e => e.Action);
                entity.HasIndex(e => e.UserId);
                entity.Property(e => e.Action).HasConversion<int>();

                entity.HasOne(e => e.User)
                    .WithMany(u => u.AuditLogs)
                    .HasForeignKey(e => e.UserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.FileTask)
                    .WithMany(ft => ft.AuditLogs)
                    .HasForeignKey(e => e.FileTaskId)
                    .OnDelete(DeleteBehavior.SetNull);

                entity.HasOne(e => e.CrossCheck)
                    .WithMany(cc => cc.AuditLogs)
                    .HasForeignKey(e => e.CrossCheckId)
                    .OnDelete(DeleteBehavior.SetNull);
            });

            // PathConfiguration entity configuration
            modelBuilder.Entity<PathConfiguration>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.HasIndex(e => e.Name);
                entity.Property(e => e.Type).HasConversion<int>();
                entity.Property(e => e.Category).HasConversion<int>();

                entity.HasOne(e => e.CreatedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.CreatedByUserId)
                    .OnDelete(DeleteBehavior.Restrict);

                entity.HasOne(e => e.ModifiedByUser)
                    .WithMany()
                    .HasForeignKey(e => e.ModifiedByUserId)
                    .OnDelete(DeleteBehavior.SetNull);
            });
        }
    }
}
