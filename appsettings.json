{"ConnectionStrings": {"DefaultConnection": "Data Source=archives.db"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "DefaultPaths": {"SourcePath": "T:\\To_Process\\Rough folder\\To Process\\restore Files", "Destinations": {"Miscellaneous": "T:\\To_Process\\Rough folder\\To Check\\Miscellaneous", "PrivateOne": "T:\\To_Process\\Rough folder\\To Check\\Private one video", "PrivateTwo": "T:\\To_Process\\Rough folder\\To Check\\Private Two", "ToBeProcessed": "T:\\To_Process\\Rough folder\\To Process\\restore Files"}}, "Security": {"EncryptionKey": "YourSecureEncryptionKeyHere_32Chars", "RequireTwoFactorForPrivate": true, "SessionTimeoutMinutes": 60, "MaxLoginAttempts": 3, "LockoutDurationMinutes": 15}, "VLC": {"LogLevel": "Error", "EnableHardwareDecoding": true, "NetworkCaching": 1000}, "CrossCheck": {"RequiredForPrivateContent": true, "DualCheckForCritical": true, "AutoAssignmentEnabled": true}}