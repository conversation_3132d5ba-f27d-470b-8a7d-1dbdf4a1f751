{% extends "base.html" %}

{% block title %}Cross-Checks - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-check-double text-primary me-3"></i>
                    Cross-Check Reviews
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshCrossChecks()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cross-Check Statistics -->
    <div class="row mb-4">
        {% set pending_count = cross_checks | selectattr('status', 'equalto', 'assigned') | list | length %}
        {% set in_progress_count = cross_checks | selectattr('status', 'equalto', 'in_progress') | list | length %}
        {% set completed_count = cross_checks | selectattr('status', 'equalto', 'completed') | list | length %}
        
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                    <h3 class="text-warning">{{ pending_count }}</h3>
                    <p class="mb-0">Pending Reviews</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-play fa-2x text-info mb-2"></i>
                    <h3 class="text-info">{{ in_progress_count }}</h3>
                    <p class="mb-0">In Progress</p>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                    <h3 class="text-success">{{ completed_count }}</h3>
                    <p class="mb-0">Completed</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Cross-Checks List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Cross-Check Queue
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-secondary" onclick="filterCrossChecks('all')">All</button>
                            <button type="button" class="btn btn-outline-warning" onclick="filterCrossChecks('assigned')">Pending</button>
                            <button type="button" class="btn btn-outline-info" onclick="filterCrossChecks('in_progress')">In Progress</button>
                            <button type="button" class="btn btn-outline-success" onclick="filterCrossChecks('completed')">Completed</button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>File</th>
                                    <th>Original Task</th>
                                    <th>Status</th>
                                    <th>Assigned To</th>
                                    <th>Quality Score</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for cross_check in cross_checks %}
                                <tr class="cross-check-row" data-status="{{ cross_check.status }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-video text-primary me-2"></i>
                                            <div>
                                                <h6 class="mb-0">{{ cross_check.file_task.file_name[:40] }}{% if cross_check.file_task.file_name|length > 40 %}...{% endif %}</h6>
                                                <small class="text-muted">
                                                    {{ (cross_check.file_task.file_size / 1024 / 1024) | round(2) }} MB
                                                    {% if cross_check.file_task.is_private_content %}
                                                    <span class="badge bg-danger ms-1">Private</span>
                                                    {% endif %}
                                                </small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if 'private' in cross_check.file_task.category else 'info' }}">
                                            {{ cross_check.file_task.category.replace('_', ' ').title() }}
                                        </span>
                                        <br>
                                        <small class="text-muted">
                                            by {{ cross_check.file_task.creator.full_name if cross_check.file_task.creator else 'Unknown' }}
                                        </small>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if cross_check.status == 'assigned' else 'info' if cross_check.status == 'in_progress' else 'success' if cross_check.status == 'completed' else 'secondary' }}">
                                            {{ cross_check.status.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <small>{{ cross_check.assigned_user.full_name }}</small>
                                        {% if cross_check.reviewed_by and cross_check.reviewed_by.id != cross_check.assigned_user.id %}
                                        <br><small class="text-muted">Reviewed by: {{ cross_check.reviewed_by.full_name }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if cross_check.quality_score %}
                                            <span class="badge bg-{{ 'success' if cross_check.quality_score >= 8 else 'warning' if cross_check.quality_score >= 6 else 'danger' }}">
                                                {{ cross_check.quality_score }}/10
                                            </span>
                                        {% else %}
                                            <small class="text-muted">Not scored</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small>{{ cross_check.created_at.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="{{ url_for('cross_check_detail', cross_check_id=cross_check.id) }}" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if cross_check.file_task.source_path and cross_check.file_task.file_name %}
                                            <a href="{{ url_for('media_player', file=cross_check.file_task.source_path + '\\' + cross_check.file_task.file_name) }}" 
                                               class="btn btn-outline-success" title="Play Video">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            {% endif %}
                                            {% if current_user.role == 'main_admin' or (cross_check.assigned_to_user_id == current_user.id and cross_check.status in ['assigned', 'in_progress']) %}
                                            <button type="button" class="btn btn-outline-warning" 
                                                    onclick="completeCrossCheck({{ cross_check.id }})" 
                                                    title="Complete Review">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not cross_checks %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-check-double text-muted fa-3x mb-3"></i>
                                        <h5 class="text-muted">No cross-checks found</h5>
                                        <p class="text-muted">
                                            {% if current_user.role in ['cross_checker_public', 'cross_checker_private'] %}
                                                No reviews have been assigned to you yet.
                                            {% else %}
                                                No cross-checks have been created yet.
                                            {% endif %}
                                        </p>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Complete Cross-Check Modal -->
<div class="modal fade" id="completeCrossCheckModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Complete Cross-Check Review</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="completeCrossCheckForm" method="POST">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="result" class="form-label">Review Result *</label>
                                <select class="form-select" id="result" name="result" required>
                                    <option value="">Select Result</option>
                                    <option value="approved">Approved</option>
                                    <option value="rejected">Rejected</option>
                                    <option value="needs_revision">Needs Revision</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quality_score" class="form-label">Quality Score (1-10)</label>
                                <input type="number" class="form-control" id="quality_score" name="quality_score" 
                                       min="1" max="10" placeholder="Rate quality 1-10">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="review_notes" class="form-label">Review Notes</label>
                        <textarea class="form-control" id="review_notes" name="review_notes" rows="4" 
                                  placeholder="Detailed review notes for internal use..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedback" class="form-label">Feedback for Executor</label>
                        <textarea class="form-control" id="feedback" name="feedback" rows="3" 
                                  placeholder="Constructive feedback for the executor..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Complete Review</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshCrossChecks() {
    window.location.reload();
}

function filterCrossChecks(status) {
    const rows = document.querySelectorAll('.cross-check-row');
    rows.forEach(row => {
        if (status === 'all' || row.dataset.status === status) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
    
    // Update active button
    document.querySelectorAll('.btn-group-sm .btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
}

function completeCrossCheck(crossCheckId) {
    document.getElementById('completeCrossCheckForm').action = `/cross-checks/${crossCheckId}/complete`;
    new bootstrap.Modal(document.getElementById('completeCrossCheckModal')).show();
}

// Auto-refresh every 30 seconds
setInterval(function() {
    if (!document.querySelector('.modal.show')) {
        refreshCrossChecks();
    }
}, 30000);
</script>
{% endblock %}
