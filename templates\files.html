{% extends "base.html" %}

{% block title %}File Browser - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-folder-open text-primary me-3"></i>
                    File Browser
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshBrowser()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    {% if current_user.role in ['main_admin', 'executor_public', 'executor_private'] %}
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createTaskModal">
                        <i class="fas fa-plus me-1"></i>Create Task
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Path Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body py-2">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <i class="fas fa-home me-1"></i>
                                <a href="{{ url_for('files') }}">Root</a>
                            </li>
                            {% set path_parts = current_path.split('\\') if '\\' in current_path else current_path.split('/') %}
                            {% for part in path_parts[:-1] %}
                            <li class="breadcrumb-item">
                                <a href="{{ url_for('files', path='\\'.join(path_parts[:loop.index])) }}">{{ part }}</a>
                            </li>
                            {% endfor %}
                            <li class="breadcrumb-item active">{{ path_parts[-1] }}</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- File List -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Contents
                        </h5>
                        <small class="text-muted">
                            {{ directories|length }} folders, {{ files|length }} video files
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <!-- Parent Directory -->
                        {% if parent_path %}
                        <a href="{{ url_for('files', path=parent_path) }}" 
                           class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-level-up-alt text-secondary me-3"></i>
                                <div>
                                    <h6 class="mb-0">.. (Parent Directory)</h6>
                                </div>
                            </div>
                        </a>
                        {% endif %}

                        <!-- Directories -->
                        {% for directory in directories %}
                        <a href="{{ url_for('files', path=directory.path) }}" 
                           class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-folder text-warning me-3"></i>
                                <div>
                                    <h6 class="mb-0">{{ directory.name }}</h6>
                                    <small class="text-muted">Folder</small>
                                </div>
                            </div>
                        </a>
                        {% endfor %}

                        <!-- Video Files -->
                        {% for file in files %}
                        <div class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-video text-primary me-3"></i>
                                    <div>
                                        <h6 class="mb-0">{{ file.name }}</h6>
                                        <small class="text-muted">
                                            {{ (file.size / 1024 / 1024) | round(2) }} MB • 
                                            {{ file.modified.strftime('%Y-%m-%d %H:%M') }}
                                        </small>
                                    </div>
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('media_player', file=file.path) }}" 
                                       class="btn btn-outline-primary" title="Play Video">
                                        <i class="fas fa-play"></i>
                                    </a>
                                    {% if current_user.role in ['main_admin', 'executor_public', 'executor_private'] %}
                                    <button type="button" class="btn btn-outline-success" 
                                            onclick="selectFileForTask('{{ file.path }}', '{{ file.name }}')" 
                                            title="Create Task">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endfor %}

                        {% if not directories and not files %}
                        <div class="list-group-item text-center py-4">
                            <i class="fas fa-folder-open text-muted fa-3x mb-3"></i>
                            <h5 class="text-muted">No files found</h5>
                            <p class="text-muted">This directory is empty or contains no video files.</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('files', path=default_paths.source_path) }}" class="btn btn-outline-primary">
                            <i class="fas fa-home me-2"></i>Go to Source
                        </a>
                        <a href="{{ url_for('tasks') }}" class="btn btn-outline-info">
                            <i class="fas fa-tasks me-2"></i>View Tasks
                        </a>
                        {% if current_user.role in ['cross_checker_public', 'cross_checker_private'] %}
                        <a href="{{ url_for('cross_checks') }}" class="btn btn-outline-warning">
                            <i class="fas fa-check-double me-2"></i>Cross-Checks
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Path Information -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Current Path
                    </h6>
                </div>
                <div class="card-body">
                    <p class="small mb-2"><strong>Location:</strong></p>
                    <code class="small">{{ current_path }}</code>
                    
                    <hr>
                    
                    <div class="small">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Folders:</span>
                            <span>{{ directories|length }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Video Files:</span>
                            <span>{{ files|length }}</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Total Size:</span>
                            <span>{{ ((files | sum(attribute='size')) / 1024 / 1024) | round(2) }} MB</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Task Modal -->
{% if current_user.role in ['main_admin', 'executor_public', 'executor_private'] %}
<div class="modal fade" id="createTaskModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create File Processing Task</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('create_file_task') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="selectedFile" class="form-label">Selected File</label>
                        <input type="text" class="form-control" id="selectedFile" readonly>
                        <input type="hidden" id="file_path" name="file_path">
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="category" class="form-label">Destination Category</label>
                                <select class="form-select" id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="miscellaneous">Miscellaneous</option>
                                    {% if current_user.role in ['main_admin', 'executor_private'] %}
                                    <option value="private_one">Private One</option>
                                    <option value="private_two">Private Two</option>
                                    {% endif %}
                                    <option value="to_be_processed">To Be Processed</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select class="form-select" id="priority" name="priority">
                                    <option value="low">Low</option>
                                    <option value="normal" selected>Normal</option>
                                    <option value="high">High</option>
                                    <option value="critical">Critical</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    {% if current_user.role == 'main_admin' %}
                    <div class="mb-3">
                        <label for="assign_to" class="form-label">Assign To</label>
                        <select class="form-select" id="assign_to" name="assign_to">
                            <option value="">Unassigned</option>
                            <!-- Will be populated via AJAX -->
                        </select>
                    </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Task</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function refreshBrowser() {
    window.location.reload();
}

function selectFileForTask(filePath, fileName) {
    document.getElementById('selectedFile').value = fileName;
    document.getElementById('file_path').value = filePath;
    new bootstrap.Modal(document.getElementById('createTaskModal')).show();
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if no modal is open
    if (!document.querySelector('.modal.show')) {
        refreshBrowser();
    }
}, 30000);
</script>
{% endblock %}
