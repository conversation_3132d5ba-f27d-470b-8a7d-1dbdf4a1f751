using Microsoft.AspNetCore.Mvc;
using ArchivesAssignmentConsole.Core.Interfaces;
using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Web.Controllers
{
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        // TODO: Add services when implemented
        // private readonly IUserService _userService;
        // private readonly IFileOperationService _fileOperationService;

        public HomeController(ILogger<HomeController> logger)
        {
            _logger = logger;
            // TODO: Inject services when implemented
            // _userService = userService;
            // _fileOperationService = fileOperationService;
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                // TODO: Get basic statistics when services are implemented
                var model = new DashboardViewModel
                {
                    TotalUsers = 1,
                    ActiveUsers = 1,
                    TotalTasks = 0,
                    PendingTasks = 0,
                    InProgressTasks = 0,
                    CompletedTasks = 0
                };

                return View(model);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading dashboard");
                return View(new DashboardViewModel());
            }
        }

        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(LoginViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View(model);
            }

            try
            {
                // Authentication logic will be implemented in the service
                // For now, redirect to dashboard
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Login error for user {Username}", model.Username);
                ModelState.AddModelError("", "Login failed. Please try again.");
                return View(model);
            }
        }

        public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View();
        }
    }

    public class DashboardViewModel
    {
        public int TotalUsers { get; set; }
        public int ActiveUsers { get; set; }
        public int TotalTasks { get; set; }
        public int PendingTasks { get; set; }
        public int InProgressTasks { get; set; }
        public int CompletedTasks { get; set; }
    }

    public class LoginViewModel
    {
        public string Username { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string? TwoFactorCode { get; set; }
        public bool RememberMe { get; set; }
    }
}
