{% extends "base.html" %}

{% block title %}Task Details - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-tasks text-primary me-3"></i>
                    Task Details
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('tasks') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>Back to Tasks
                    </a>
                    {% if task.source_path and task.file_name %}
                    <a href="{{ url_for('media_player', file=task.source_path + '\\' + task.file_name) }}" 
                       class="btn btn-primary">
                        <i class="fas fa-play me-1"></i>Play Video
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Task Information -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-info-circle me-2"></i>
                            Task Information
                        </h5>
                        <span class="badge bg-{{ 'warning' if task.status == 'pending' else 'info' if task.status == 'in_progress' else 'success' if task.status in ['completed', 'approved'] else 'danger' if task.status == 'rejected' else 'secondary' }} fs-6">
                            {{ task.status.replace('_', ' ').title() }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>File Details</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>File Name:</strong></td>
                                    <td>{{ task.file_name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>File Size:</strong></td>
                                    <td>{{ (task.file_size / 1024 / 1024) | round(2) }} MB</td>
                                </tr>
                                <tr>
                                    <td><strong>Source Path:</strong></td>
                                    <td><code class="small">{{ task.source_path }}</code></td>
                                </tr>
                                <tr>
                                    <td><strong>Category:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if 'private' in task.category else 'info' }}">
                                            {{ task.category.replace('_', ' ').title() }}
                                        </span>
                                        {% if task.is_private_content %}
                                        <span class="badge bg-danger ms-1">Private Content</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Priority:</strong></td>
                                    <td>
                                        <span class="badge bg-{{ 'danger' if task.priority == 'critical' else 'warning' if task.priority == 'high' else 'info' if task.priority == 'normal' else 'secondary' }}">
                                            {{ task.priority.title() }}
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Task Management</h6>
                            <table class="table table-sm">
                                <tr>
                                    <td><strong>Created By:</strong></td>
                                    <td>{{ task.creator.full_name if task.creator else 'Unknown' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Assigned To:</strong></td>
                                    <td>{{ task.assigned_user.full_name if task.assigned_user else 'Unassigned' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created:</strong></td>
                                    <td>{{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Started:</strong></td>
                                    <td>{{ task.started_at.strftime('%Y-%m-%d %H:%M:%S') if task.started_at else 'Not started' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Completed:</strong></td>
                                    <td>{{ task.completed_at.strftime('%Y-%m-%d %H:%M:%S') if task.completed_at else 'Not completed' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    {% if task.notes %}
                    <hr>
                    <h6>Task Notes</h6>
                    <div class="alert alert-light">
                        {{ task.notes }}
                    </div>
                    {% endif %}
                    
                    {% if task.processing_notes %}
                    <hr>
                    <h6>Processing Notes</h6>
                    <div class="alert alert-info">
                        <pre class="mb-0">{{ task.processing_notes }}</pre>
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- Update Status -->
            {% if current_user.role == 'main_admin' or (task.assigned_to_user_id == current_user.id and task.status in ['assigned', 'in_progress']) %}
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-edit me-2"></i>
                        Update Task Status
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('update_task_status', task_id=task.id) }}" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">New Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="pending" {{ 'selected' if task.status == 'pending' else '' }}>Pending</option>
                                        <option value="in_progress" {{ 'selected' if task.status == 'in_progress' else '' }}>In Progress</option>
                                        <option value="completed" {{ 'selected' if task.status == 'completed' else '' }}>Completed</option>
                                        <option value="on_hold" {{ 'selected' if task.status == 'on_hold' else '' }}>On Hold</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="notes" class="form-label">Update Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="Add notes about this status change..."></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Status
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Cross-Checks & Actions -->
        <div class="col-lg-4">
            <!-- Cross-Checks -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-check-double me-2"></i>
                            Cross-Checks
                        </h6>
                        {% if current_user.role == 'main_admin' and not cross_checks %}
                        <button type="button" class="btn btn-outline-primary btn-sm" 
                                data-bs-toggle="modal" data-bs-target="#createCrossCheckModal">
                            <i class="fas fa-plus me-1"></i>Create
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body">
                    {% if cross_checks %}
                        {% for cross_check in cross_checks %}
                        <div class="border rounded p-3 mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="badge bg-{{ 'warning' if cross_check.status == 'assigned' else 'info' if cross_check.status == 'in_progress' else 'success' if cross_check.status == 'completed' else 'secondary' }}">
                                    {{ cross_check.status.replace('_', ' ').title() }}
                                </span>
                                {% if cross_check.quality_score %}
                                <span class="badge bg-{{ 'success' if cross_check.quality_score >= 8 else 'warning' if cross_check.quality_score >= 6 else 'danger' }}">
                                    {{ cross_check.quality_score }}/10
                                </span>
                                {% endif %}
                            </div>
                            <p class="small mb-1">
                                <strong>Assigned to:</strong> {{ cross_check.assigned_user.full_name }}
                            </p>
                            {% if cross_check.reviewed_by %}
                            <p class="small mb-1">
                                <strong>Reviewed by:</strong> {{ cross_check.reviewed_by.full_name }}
                            </p>
                            {% endif %}
                            <p class="small mb-1">
                                <strong>Created:</strong> {{ cross_check.created_at.strftime('%Y-%m-%d %H:%M') }}
                            </p>
                            {% if cross_check.completed_at %}
                            <p class="small mb-1">
                                <strong>Completed:</strong> {{ cross_check.completed_at.strftime('%Y-%m-%d %H:%M') }}
                            </p>
                            {% endif %}
                            {% if cross_check.result %}
                            <p class="small mb-1">
                                <strong>Result:</strong> 
                                <span class="badge bg-{{ 'success' if cross_check.result == 'approved' else 'danger' if cross_check.result == 'rejected' else 'warning' }}">
                                    {{ cross_check.result.replace('_', ' ').title() }}
                                </span>
                            </p>
                            {% endif %}
                            {% if cross_check.feedback %}
                            <hr>
                            <p class="small mb-0">
                                <strong>Feedback:</strong><br>
                                {{ cross_check.feedback }}
                            </p>
                            {% endif %}
                            <div class="mt-2">
                                <a href="{{ url_for('cross_check_detail', cross_check_id=cross_check.id) }}" 
                                   class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-double text-muted fa-2x mb-2"></i>
                            <p class="text-muted mb-0">No cross-checks yet</p>
                            {% if task.is_private_content %}
                            <small class="text-muted">Cross-check will be auto-created when task is completed</small>
                            {% endif %}
                        </div>
                    {% endif %}
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-bolt me-2"></i>
                        Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if task.source_path and task.file_name %}
                        <a href="{{ url_for('media_player', file=task.source_path + '\\' + task.file_name) }}" 
                           class="btn btn-outline-primary">
                            <i class="fas fa-play me-2"></i>Play Video
                        </a>
                        {% endif %}
                        
                        <a href="{{ url_for('files', path=task.source_path) }}" 
                           class="btn btn-outline-info">
                            <i class="fas fa-folder-open me-2"></i>Browse Source
                        </a>
                        
                        {% if current_user.role == 'main_admin' %}
                        <button type="button" class="btn btn-outline-warning" 
                                onclick="copyTaskInfo()">
                            <i class="fas fa-copy me-2"></i>Copy Task Info
                        </button>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Cross-Check Modal -->
{% if current_user.role == 'main_admin' %}
<div class="modal fade" id="createCrossCheckModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create Cross-Check</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('create_cross_check', task_id=task.id) }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="assigned_to" class="form-label">Assign To Cross-Checker</label>
                        <select class="form-select" id="assigned_to" name="assigned_to" required>
                            <option value="">Select Cross-Checker</option>
                            <!-- Will be populated based on content type -->
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Cross-Check</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function copyTaskInfo() {
    const taskInfo = `Task: {{ task.file_name }}
Status: {{ task.status }}
Category: {{ task.category }}
Priority: {{ task.priority }}
Created: {{ task.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
Assigned to: {{ task.assigned_user.full_name if task.assigned_user else 'Unassigned' }}`;
    
    navigator.clipboard.writeText(taskInfo).then(function() {
        ArchivesConsole.showNotification('Task information copied to clipboard!', 'success');
    });
}
</script>
{% endblock %}
