2025-06-14 15:45:15,707 - __main__ - INFO - Created default admin user: admin/admin123
2025-06-14 15:45:15,708 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 15:45:15,762 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://**************:5004
2025-06-14 15:45:15,763 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-14 15:45:15,785 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-14 15:45:16,680 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 15:45:16,700 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 15:45:16,706 - werkzeug - INFO -  * Debugger PIN: 653-556-705
2025-06-14 15:45:36,210 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:36] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-14 15:45:36,248 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:36] "GET /login HTTP/1.1" 200 -
2025-06-14 15:45:37,055 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-14 15:48:32,753 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-14 15:48:32,825 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "GET / HTTP/1.1" 200 -
2025-06-14 15:48:32,933 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-14 15:48:33,186 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:33] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-14 15:49:40,914 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:40] "GET / HTTP/1.1" 200 -
2025-06-14 15:49:41,209 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 15:49:41,244 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:16:20,288 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Archives Backlog Project\\app.py', reloading
2025-06-14 16:16:20,296 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\Documents\\augment-projects\\Archives Backlog Project\\app.py', reloading
2025-06-14 16:16:21,161 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-14 16:25:19,017 - __main__ - INFO - Created default path configurations
2025-06-14 16:25:19,020 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 16:25:19,021 - __main__ - INFO - Default users created:
2025-06-14 16:25:19,021 - __main__ - INFO -   admin/admin123 (Main Admin)
2025-06-14 16:25:19,022 - __main__ - INFO -   executor_pub/password123 (Public Executor)
2025-06-14 16:25:19,022 - __main__ - INFO -   executor_priv/password123 (Private Executor - 2FA required)
2025-06-14 16:25:19,025 - __main__ - INFO -   checker_pub/password123 (Public Cross-Checker)
2025-06-14 16:25:19,025 - __main__ - INFO -   checker_priv/password123 (Private Cross-Checker - 2FA required)
2025-06-14 16:25:19,026 - __main__ - INFO -   viewer/password123 (Viewer)
2025-06-14 16:25:19,090 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://**************:5004
2025-06-14 16:25:19,095 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-14 16:25:19,119 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-14 16:25:20,464 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 16:25:20,465 - __main__ - INFO - Default users created:
2025-06-14 16:25:20,465 - __main__ - INFO -   admin/admin123 (Main Admin)
2025-06-14 16:25:20,465 - __main__ - INFO -   executor_pub/password123 (Public Executor)
2025-06-14 16:25:20,465 - __main__ - INFO -   executor_priv/password123 (Private Executor - 2FA required)
2025-06-14 16:25:20,466 - __main__ - INFO -   checker_pub/password123 (Public Cross-Checker)
2025-06-14 16:25:20,466 - __main__ - INFO -   checker_priv/password123 (Private Cross-Checker - 2FA required)
2025-06-14 16:25:20,466 - __main__ - INFO -   viewer/password123 (Viewer)
2025-06-14 16:25:20,493 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 16:25:20,499 - werkzeug - INFO -  * Debugger PIN: 653-556-705
2025-06-14 16:25:46,278 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:46] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-14 16:25:46,305 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:46] "GET /login HTTP/1.1" 200 -
2025-06-14 16:25:49,805 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:49] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-14 16:25:49,881 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:49] "GET / HTTP/1.1" 200 -
2025-06-14 16:25:50,330 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:50] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:25:50,332 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:25:50] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:34,331 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:34] "GET / HTTP/1.1" 200 -
2025-06-14 16:27:34,612 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:34] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:34,661 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:34] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:27:38,510 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:38] "GET /users HTTP/1.1" 200 -
2025-06-14 16:27:38,569 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:38] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:38,725 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:38] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:27:43,242 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:43] "GET /users/create HTTP/1.1" 200 -
2025-06-14 16:27:43,554 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:43] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:43,587 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:43] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:27:51,595 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:51] "GET /users HTTP/1.1" 200 -
2025-06-14 16:27:51,670 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:51] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:51,851 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:51] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:27:55,229 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:55] "GET / HTTP/1.1" 200 -
2025-06-14 16:27:55,530 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:55] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:27:55,569 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:27:55] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:30:59,352 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:30:59] "GET / HTTP/1.1" 200 -
2025-06-14 16:30:59,392 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:30:59] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:30:59,581 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:30:59] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:30:59,934 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:30:59] "GET / HTTP/1.1" 200 -
2025-06-14 16:31:00,005 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:31:00,170 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:31:00,700 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:00] "GET / HTTP/1.1" 200 -
2025-06-14 16:31:00,767 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:00] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 16:31:00,942 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:00] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
2025-06-14 16:31:03,751 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\flask\\app.py', reloading
2025-06-14 16:31:03,786 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\flask_login\\utils.py', reloading
2025-06-14 16:31:03,792 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\flask\\templating.py', reloading
2025-06-14 16:31:03,800 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jinja2\\environment.py', reloading
2025-06-14 16:31:03,806 - werkzeug - INFO -  * Detected change in 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\Lib\\site-packages\\jinja2\\loaders.py', reloading
2025-06-14 16:31:03,824 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:03] "[35m[1mGET /reports HTTP/1.1[0m" 500 -
2025-06-14 16:31:04,035 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:04] "GET /reports?__debugger__=yes&cmd=resource&f=style.css HTTP/1.1" 200 -
2025-06-14 16:31:04,182 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:04] "GET /reports?__debugger__=yes&cmd=resource&f=debugger.js HTTP/1.1" 200 -
2025-06-14 16:31:04,394 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-14 16:31:05,957 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 16:31:05,966 - __main__ - INFO - Default users created:
2025-06-14 16:31:05,967 - __main__ - INFO -   admin/admin123 (Main Admin)
2025-06-14 16:31:05,967 - __main__ - INFO -   executor_pub/password123 (Public Executor)
2025-06-14 16:31:05,968 - __main__ - INFO -   executor_priv/password123 (Private Executor - 2FA required)
2025-06-14 16:31:05,969 - __main__ - INFO -   checker_pub/password123 (Public Cross-Checker)
2025-06-14 16:31:05,970 - __main__ - INFO -   checker_priv/password123 (Private Cross-Checker - 2FA required)
2025-06-14 16:31:05,970 - __main__ - INFO -   viewer/password123 (Viewer)
2025-06-14 16:31:06,045 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 16:31:06,052 - werkzeug - INFO -  * Debugger PIN: 653-556-705
2025-06-14 16:31:06,252 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 16:31:06] "GET /reports?__debugger__=yes&cmd=resource&f=console.png HTTP/1.1" 200 -
