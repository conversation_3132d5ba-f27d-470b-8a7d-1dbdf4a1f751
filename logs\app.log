2025-06-14 15:45:15,707 - __main__ - INFO - Created default admin user: admin/admin123
2025-06-14 15:45:15,708 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 15:45:15,762 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5004
 * Running on http://**************:5004
2025-06-14 15:45:15,763 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-06-14 15:45:15,785 - werkzeug - INFO -  * Restarting with watchdog (windowsapi)
2025-06-14 15:45:16,680 - __main__ - INFO - Starting Archives Assignment Console on port 5004...
2025-06-14 15:45:16,700 - werkzeug - WARNING -  * Debugger is active!
2025-06-14 15:45:16,706 - werkzeug - INFO -  * Debugger PIN: 653-556-705
2025-06-14 15:45:36,210 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:36] "[32mGET / HTTP/1.1[0m" 302 -
2025-06-14 15:45:36,248 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:36] "GET /login HTTP/1.1" 200 -
2025-06-14 15:45:37,055 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:45:37] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-14 15:48:32,753 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-06-14 15:48:32,825 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "GET / HTTP/1.1" 200 -
2025-06-14 15:48:32,933 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:32] "GET /static/css/style.css HTTP/1.1" 200 -
2025-06-14 15:48:33,186 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:48:33] "GET /static/js/app.js HTTP/1.1" 200 -
2025-06-14 15:49:40,914 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:40] "GET / HTTP/1.1" 200 -
2025-06-14 15:49:41,209 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:41] "[36mGET /static/css/style.css HTTP/1.1[0m" 304 -
2025-06-14 15:49:41,244 - werkzeug - INFO - 127.0.0.1 - - [14/Jun/2025 15:49:41] "[36mGET /static/js/app.js HTTP/1.1[0m" 304 -
