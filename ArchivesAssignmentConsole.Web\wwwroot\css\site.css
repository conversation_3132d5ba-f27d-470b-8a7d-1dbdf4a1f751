/* Archives Assignment Console Styles */

html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}

/* Custom styles for Archives Assignment Console */
.navbar-brand {
  font-weight: bold;
  font-size: 1.5rem;
}

.card {
  border: none;
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  transition: box-shadow 0.15s ease-in-out;
}

.card:hover {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  font-weight: 600;
}

.progress {
  height: 1.5rem;
}

.progress-bar {
  font-size: 0.875rem;
  font-weight: 500;
}

.alert {
  border: none;
  border-radius: 0.5rem;
}

.btn {
  border-radius: 0.375rem;
  font-weight: 500;
}

.btn i {
  margin-right: 0.25rem;
}

/* Dashboard specific styles */
.display-4 {
  font-weight: 300;
  color: #495057;
}

.card-body h4 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.card-body p {
  font-size: 0.875rem;
  margin-bottom: 0;
  opacity: 0.8;
}

/* Status badges */
.badge {
  font-size: 0.75rem;
  padding: 0.375rem 0.75rem;
}

/* Media player styles */
.media-player-container {
  background-color: #000;
  border-radius: 0.5rem;
  overflow: hidden;
}

.video-controls {
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 1rem;
}

/* File browser styles */
.file-browser {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 0.375rem;
}

.file-item {
  padding: 0.75rem;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: background-color 0.15s ease-in-out;
}

.file-item:hover {
  background-color: #f8f9fa;
}

.file-item:last-child {
  border-bottom: none;
}

.file-icon {
  width: 24px;
  text-align: center;
  margin-right: 0.75rem;
}

/* Task status colors */
.status-pending { color: #ffc107; }
.status-in-progress { color: #17a2b8; }
.status-completed { color: #28a745; }
.status-cross-check { color: #fd7e14; }
.status-approved { color: #28a745; }
.status-rejected { color: #dc3545; }

/* Role-based styling */
.role-main-admin { border-left: 4px solid #6f42c1; }
.role-executor-public { border-left: 4px solid #17a2b8; }
.role-executor-private { border-left: 4px solid #dc3545; }
.role-cross-checker-public { border-left: 4px solid #28a745; }
.role-cross-checker-private { border-left: 4px solid #fd7e14; }
.role-viewer { border-left: 4px solid #6c757d; }

/* Responsive adjustments */
@media (max-width: 768px) {
  .display-4 {
    font-size: 2rem;
  }
  
  .card-body h4 {
    font-size: 1.5rem;
  }
  
  .navbar-brand {
    font-size: 1.25rem;
  }
}

/* Loading spinner */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
