{% extends "base.html" %}

{% block title %}Media Player - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-play-circle text-primary me-3"></i>
                    Media Player
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('files') }}" class="btn btn-secondary">
                        <i class="fas fa-folder-open me-1"></i>Back to Files
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleFullscreen()">
                        <i class="fas fa-expand me-1"></i>Fullscreen
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-9">
            <!-- Video Player -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="media-player-container" id="playerContainer">
                        <video id="videoPlayer" class="w-100" controls preload="metadata"
                               style="max-height: 70vh; background: #000;">
                            <!-- Multiple source formats for better codec support -->
                            {% set file_ext = file_path.split('.')[-1].lower() %}
                            {% if file_ext == 'mp4' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/mp4; codecs='avc1.42E01E, mp4a.40.2'">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/mp4">
                            {% elif file_ext == 'mov' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/quicktime">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/mp4">
                            {% elif file_ext == 'avi' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/x-msvideo">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/avi">
                            {% elif file_ext == 'mkv' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/x-matroska">
                            {% elif file_ext == 'webm' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/webm; codecs='vp8, vorbis'">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/webm">
                            {% elif file_ext == 'wmv' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/x-ms-wmv">
                            {% elif file_ext == 'flv' %}
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/x-flv">
                            {% else %}
                                <!-- Fallback for unknown formats -->
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/mp4">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/quicktime">
                                <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/x-msvideo">
                            {% endif %}

                            <!-- Fallback content for unsupported browsers/codecs -->
                            <div class="text-center text-white p-4">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
                                <h5>MOV Codec Issue Detected</h5>
                                <p class="mb-3">This MOV file uses a codec that web browsers cannot display properly.</p>
                                <div class="alert alert-warning text-dark">
                                    <h6><i class="fas fa-info-circle me-2"></i>Common Issue with Professional MOV Files</h6>
                                    <p class="mb-2">MOV files from video editing software often use codecs like:</p>
                                    <ul class="text-start mb-2">
                                        <li>ProRes (Apple Professional codec)</li>
                                        <li>DNxHD/DNxHR (Avid codec)</li>
                                        <li>Uncompressed RGB</li>
                                        <li>Custom QuickTime codecs</li>
                                    </ul>
                                    <p class="mb-0">These require specialized players like VLC.</p>
                                </div>

                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Codec Information</h6>
                                    <p class="mb-2"><strong>File Format:</strong> {{ file_ext.upper() }}</p>
                                    <p class="mb-2"><strong>Supported Browsers:</strong> Chrome, Firefox, Safari, Edge</p>
                                    <p class="mb-0"><strong>Recommended:</strong> Convert to MP4 (H.264) for best compatibility</p>
                                </div>

                                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                                    <button type="button" class="btn btn-success btn-lg" onclick="openWithVLC()">
                                        <i class="fas fa-play me-2"></i>Open in VLC Player
                                    </button>
                                    <a href="{{ url_for('stream_file', filename=file_path) }}"
                                       class="btn btn-primary" download>
                                        <i class="fas fa-download me-2"></i>Download File
                                    </a>
                                    <button type="button" class="btn btn-info" onclick="showVLCInstructions()">
                                        <i class="fas fa-question-circle me-2"></i>VLC Help
                                    </button>
                                </div>

                                <div class="mt-3">
                                    <small class="text-muted">
                                        <strong>Quick Fix:</strong> Download VLC Media Player (free) to play this file perfectly.
                                    </small>
                                </div>
                            </div>
                        </video>
                        
                        <!-- Custom Controls Overlay -->
                        <div class="video-controls-overlay" id="controlsOverlay">
                            <div class="d-flex justify-content-center align-items-center gap-3">
                                <button type="button" class="btn btn-outline-light btn-lg" onclick="skipBackward()">
                                    <i class="fas fa-backward"></i>
                                </button>
                                <button type="button" class="btn btn-light btn-lg" id="playPauseBtn" onclick="togglePlayPause()">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-lg" onclick="skipForward()">
                                    <i class="fas fa-forward"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Playback Information -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Playback Information</h6>
                            <div class="small">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Current Time:</span>
                                    <span id="currentTime">00:00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Duration:</span>
                                    <span id="duration">00:00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Volume:</span>
                                    <span id="volume">100%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Playback Rate:</span>
                                    <span id="playbackRate">1x</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-sliders-h me-2"></i>Quick Controls</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(0.5)">0.5x Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(1)">Normal Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(1.5)">1.5x Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(2)">2x Speed</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Information Sidebar -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-file-video me-2"></i>
                        File Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="small text-muted">File Name:</label>
                        <p class="small mb-0">{{ file_path.split('\\')[-1] if '\\' in file_path else file_path.split('/')[-1] }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">File Path:</label>
                        <p class="small mb-0 text-break">{{ file_path }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">File Size:</label>
                        <p class="small mb-0" id="fileSize">Calculating...</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">Resolution:</label>
                        <p class="small mb-0" id="resolution">Loading...</p>
                    </div>

                    <div class="mb-3">
                        <label class="small text-muted">Format:</label>
                        <p class="small mb-0">{{ file_path.split('.')[-1].upper() }}</p>
                    </div>

                    <div class="mb-3">
                        <label class="small text-muted">Codec:</label>
                        <p class="small mb-0" id="codecInfo">Detecting...</p>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        {% if current_user.role in ['main_admin', 'executor_public', 'executor_private'] %}
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="createTaskFromVideo()">
                            <i class="fas fa-plus me-1"></i>Create Task
                        </button>
                        {% endif %}
                        
                        <a href="{{ url_for('stream_file', filename=file_path) }}" 
                           class="btn btn-outline-primary btn-sm" download>
                            <i class="fas fa-download me-1"></i>Download
                        </a>
                        
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="copyFilePath()">
                            <i class="fas fa-copy me-1"></i>Copy Path
                        </button>
                    </div>
                </div>
            </div>

            <!-- Playback History -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Session Info
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Started:</span>
                            <span id="sessionStart">--:--:--</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Watch Time:</span>
                            <span id="watchTime">00:00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>User:</span>
                            <span>{{ current_user.full_name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.media-player-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-controls-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.media-player-container:hover .video-controls-overlay {
    opacity: 1;
    pointer-events: all;
}

#videoPlayer {
    border-radius: 8px;
}

.btn-group-vertical .btn {
    border-radius: 0;
}

.btn-group-vertical .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.btn-group-vertical .btn:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
const video = document.getElementById('videoPlayer');
const playPauseBtn = document.getElementById('playPauseBtn');
let sessionStartTime = new Date();
let totalWatchTime = 0;

// Initialize player with codec detection
video.addEventListener('loadedmetadata', function() {
    updateDuration();
    updateResolution();
    updateFileSize();
    updateCodecInfo();
    console.log('Video loaded successfully');

    // Check if video is actually playing (has video track)
    setTimeout(checkVideoPlayback, 2000);
});

video.addEventListener('timeupdate', function() {
    updateCurrentTime();
    updateWatchTime();
});

video.addEventListener('play', function() {
    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
});

video.addEventListener('pause', function() {
    playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
});

video.addEventListener('volumechange', function() {
    updateVolume();
});

// Error handling for codec issues
video.addEventListener('error', function(e) {
    console.error('Video error:', e);
    handleVideoError(e);
});

video.addEventListener('loadstart', function() {
    console.log('Video loading started...');
});

video.addEventListener('canplay', function() {
    console.log('Video can start playing');
});

video.addEventListener('canplaythrough', function() {
    console.log('Video can play through without buffering');
});

function togglePlayPause() {
    if (video.paused) {
        video.play();
    } else {
        video.pause();
    }
}

function skipBackward() {
    video.currentTime = Math.max(0, video.currentTime - 10);
}

function skipForward() {
    video.currentTime = Math.min(video.duration, video.currentTime + 10);
}

function setPlaybackRate(rate) {
    video.playbackRate = rate;
    document.getElementById('playbackRate').textContent = rate + 'x';
}

function toggleFullscreen() {
    const container = document.getElementById('playerContainer');
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        container.requestFullscreen();
    }
}

function updateCurrentTime() {
    document.getElementById('currentTime').textContent = formatTime(video.currentTime);
}

function updateDuration() {
    document.getElementById('duration').textContent = formatTime(video.duration);
}

function updateVolume() {
    document.getElementById('volume').textContent = Math.round(video.volume * 100) + '%';
}

function updateResolution() {
    document.getElementById('resolution').textContent = video.videoWidth + 'x' + video.videoHeight;
}

function updateFileSize() {
    // This would need to be passed from the server
    document.getElementById('fileSize').textContent = 'Unknown';
}

function updateCodecInfo() {
    // Get detailed codec information from server using POST for better path handling
    const filePath = '{{ file_path }}';

    fetch('/codec-info-post', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            'file_path': filePath
        })
    })
        .then(response => response.json())
        .then(data => {
            const codecElement = document.getElementById('codecInfo');
            if (codecElement && data.detailed_analysis) {
                const analysis = data.detailed_analysis;
                codecElement.innerHTML = `
                    <div class="small">
                        <strong>Video:</strong> ${analysis.video_codec || 'Unknown'}<br>
                        <strong>Audio:</strong> ${analysis.audio_codec || 'Unknown'}<br>
                        <strong>Compatible:</strong> ${analysis.browser_compatible ? '✓ Yes' : '✗ No'}
                    </div>
                `;

                // Show issues if any
                if (analysis.issues && analysis.issues.length > 0) {
                    showCodecIssues(analysis.issues);
                }

                // If not browser compatible, show alternatives
                if (!analysis.browser_compatible) {
                    showCodecAlternatives(analysis);
                }
            }
        })
        .catch(error => {
            console.error('Error getting codec info:', error);
            const codecElement = document.getElementById('codecInfo');
            if (codecElement) {
                codecElement.textContent = 'Analysis failed';
            }
        });
}

function checkVideoPlayback() {
    // Check if video is actually displaying video content
    if (video.videoWidth === 0 || video.videoHeight === 0) {
        console.warn('Video dimensions are 0x0 - likely audio-only or codec issue');
        showMOVCodecWarning();
    } else if (video.readyState >= 2 && video.paused) {
        // Video loaded but might have codec issues
        console.log('Video loaded, dimensions:', video.videoWidth + 'x' + video.videoHeight);
    }

    // Additional check for MOV files specifically
    const fileName = '{{ file_path }}';
    if (fileName.toLowerCase().endsWith('.mov')) {
        // Give it a moment to load, then check again
        setTimeout(() => {
            if (video.videoWidth === 0 || video.videoHeight === 0) {
                showMOVCodecWarning();
            }
        }, 5000);
    }
}

function showMOVCodecWarning() {
    // Remove any existing warnings
    const existingWarning = document.getElementById('movCodecWarning');
    if (existingWarning) {
        existingWarning.remove();
    }

    const warningHtml = `
        <div class="alert alert-warning mt-3" id="movCodecWarning">
            <div class="row">
                <div class="col-md-8">
                    <h5><i class="fas fa-exclamation-triangle me-2"></i>MOV Codec Issue Detected</h5>
                    <p class="mb-2">This MOV file cannot be displayed in web browsers due to codec compatibility.</p>
                    <p class="mb-0"><strong>Solution:</strong> Use VLC Media Player for perfect playback.</p>
                </div>
                <div class="col-md-4 text-end">
                    <button type="button" class="btn btn-success btn-lg" onclick="openWithVLC()">
                        <i class="fas fa-external-link-alt me-2"></i>Open in VLC
                    </button>
                </div>
            </div>
        </div>
    `;

    const playerContainer = document.getElementById('playerContainer');
    playerContainer.insertAdjacentHTML('afterend', warningHtml);

    // Show notification
    ArchivesConsole.showNotification('MOV codec issue detected. Use VLC for best results.', 'warning');
}

function showVideoOnlyAudioWarning() {
    const warningHtml = `
        <div class="alert alert-warning mt-3" id="codecWarning">
            <h6><i class="fas fa-exclamation-triangle me-2"></i>Video Codec Issue Detected</h6>
            <p class="mb-2">This file appears to have audio but no video display. This is typically caused by:</p>
            <ul class="mb-3">
                <li>Unsupported video codec (common with MOV files)</li>
                <li>Browser compatibility issues</li>
                <li>Corrupted video stream</li>
            </ul>
            <div class="d-grid gap-2 d-md-flex">
                <button type="button" class="btn btn-warning btn-sm" onclick="openInVLC()">
                    <i class="fas fa-external-link-alt me-1"></i>Open in VLC
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="downloadFile()">
                    <i class="fas fa-download me-1"></i>Download File
                </button>
                <button type="button" class="btn btn-secondary btn-sm" onclick="getDetailedCodecInfo()">
                    <i class="fas fa-info-circle me-1"></i>Codec Details
                </button>
            </div>
        </div>
    `;

    const playerContainer = document.getElementById('playerContainer');
    playerContainer.insertAdjacentHTML('afterend', warningHtml);
}

function showCodecIssues(issues) {
    console.warn('Codec issues detected:', issues);

    const issuesHtml = `
        <div class="alert alert-info mt-2" id="codecIssues">
            <h6><i class="fas fa-info-circle me-2"></i>Codec Information</h6>
            <ul class="mb-0">
                ${issues.map(issue => `<li>${issue}</li>`).join('')}
            </ul>
        </div>
    `;

    const codecElement = document.getElementById('codecInfo');
    if (codecElement) {
        codecElement.insertAdjacentHTML('afterend', issuesHtml);
    }
}

function showCodecAlternatives(analysis) {
    const alternativesHtml = `
        <div class="mt-2">
            <small class="text-muted">
                <strong>Recommended:</strong> Convert to MP4 (H.264/AAC) for web compatibility
            </small>
        </div>
    `;

    const codecElement = document.getElementById('codecInfo');
    if (codecElement) {
        codecElement.insertAdjacentHTML('afterend', alternativesHtml);
    }
}

function openWithVLC() {
    const fileUrl = '{{ url_for("stream_file", filename=file_path, _external=True) }}';
    const fileName = '{{ file_path.split("\\")[-1] if "\\" in file_path else file_path.split("/")[-1] }}';

    // Try VLC protocol first
    const vlcProtocol = `vlc://${fileUrl}`;

    try {
        // Attempt to open with VLC protocol
        window.location.href = vlcProtocol;

        // Show success message
        ArchivesConsole.showNotification('Opening in VLC Player...', 'success');

        // Show fallback instructions after a delay
        setTimeout(() => {
            showVLCInstructions();
        }, 3000);

    } catch (error) {
        console.error('VLC protocol failed:', error);
        showVLCInstructions();
    }
}

function showVLCInstructions() {
    const fileUrl = '{{ url_for("stream_file", filename=file_path, _external=True) }}';
    const fileName = '{{ file_path.split("\\")[-1] if "\\" in file_path else file_path.split("/")[-1] }}';

    const instructions = `
🎬 VLC PLAYER SOLUTION FOR MOV FILES

Your MOV file: ${fileName}

📥 OPTION 1: Download VLC (Recommended)
1. Download VLC Media Player (free): https://www.videolan.org/vlc/
2. Install VLC on your computer
3. Download the video file using the "Download File" button
4. Double-click the downloaded file to open in VLC

🌐 OPTION 2: Stream in VLC
1. Open VLC Media Player
2. Go to: Media → Open Network Stream (Ctrl+N)
3. Enter this URL: ${fileUrl}
4. Click "Play"

⚡ OPTION 3: VLC Protocol (if VLC is installed)
Click this link: ${vlcProtocol}

🔧 WHY THIS HAPPENS:
MOV files from professional video editing often use codecs like:
• Apple ProRes
• Avid DNxHD/DNxHR
• Uncompressed formats
• Custom QuickTime codecs

These codecs are not supported by web browsers but work perfectly in VLC.

💡 TIP: For web compatibility, convert MOV files to MP4 (H.264) format.
    `;

    alert(instructions);
}

function openInVLC() {
    // Legacy function - redirect to new function
    openWithVLC();
}

function downloadFile() {
    const downloadUrl = '{{ url_for("stream_file", filename=file_path) }}';
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = '{{ file_path.split("\\")[-1] if "\\" in file_path else file_path.split("/")[-1] }}';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    ArchivesConsole.showNotification('Download started. Open with VLC for best results.', 'info');
}

function getDetailedCodecInfo() {
    const filePath = '{{ file_path }}';

    fetch('/codec-info-post', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            'file_path': filePath
        })
    })
        .then(response => response.json())
        .then(data => {
            const analysis = data.detailed_analysis;
            const info = `
Detailed Codec Analysis:

File: ${data.filename}
Format: ${data.extension.toUpperCase()}
Size: ${data.size_mb} MB

Video Codec: ${analysis.video_codec || 'Unknown'}
Audio Codec: ${analysis.audio_codec || 'Unknown'}
Resolution: ${analysis.resolution || 'Unknown'}
Duration: ${analysis.duration ? Math.round(analysis.duration) + 's' : 'Unknown'}

Browser Compatible: ${analysis.browser_compatible ? 'Yes' : 'No'}

Issues Found:
${analysis.issues.length > 0 ? analysis.issues.join('\n') : 'None'}

Recommendation:
${data.recommendations}
            `;

            alert(info);
        })
        .catch(error => {
            alert('Could not retrieve detailed codec information: ' + error.message);
        });
}

function getCodecInfo(extension) {
    const codecMap = {
        'mp4': 'H.264/AAC',
        'mov': 'QuickTime/H.264',
        'avi': 'Various/PCM',
        'mkv': 'H.264/Matroska',
        'webm': 'VP8/Vorbis',
        'wmv': 'WMV/WMA',
        'flv': 'H.264/AAC'
    };
    return codecMap[extension] || 'Unknown';
}

function handleVideoError(error) {
    console.error('Video playback error:', error);

    let errorMessage = 'Unknown error occurred';
    if (video.error) {
        switch(video.error.code) {
            case video.error.MEDIA_ERR_ABORTED:
                errorMessage = 'Video playback was aborted';
                break;
            case video.error.MEDIA_ERR_NETWORK:
                errorMessage = 'Network error occurred while loading video';
                break;
            case video.error.MEDIA_ERR_DECODE:
                errorMessage = 'Video codec not supported or file corrupted';
                break;
            case video.error.MEDIA_ERR_SRC_NOT_SUPPORTED:
                errorMessage = 'Video format not supported by browser';
                break;
        }
    }

    // Show error message
    showVideoError(errorMessage);
}

function showVideoError(message) {
    const playerContainer = document.getElementById('playerContainer');
    const fileExt = '{{ file_path.split(".")[-1].lower() }}';

    playerContainer.innerHTML = `
        <div class="text-center text-white p-4" style="background: #000; border-radius: 8px;">
            <i class="fas fa-exclamation-triangle fa-3x mb-3 text-warning"></i>
            <h5>Video Playback Error</h5>
            <p class="mb-3">${message}</p>

            <div class="alert alert-warning">
                <h6><i class="fas fa-info-circle me-2"></i>Troubleshooting</h6>
                <p class="mb-2"><strong>File Format:</strong> ${fileExt.toUpperCase()}</p>
                <p class="mb-2"><strong>Issue:</strong> Browser codec compatibility</p>
                <p class="mb-0"><strong>Solution:</strong> Try downloading or use VLC player</p>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                <a href="{{ url_for('stream_file', filename=file_path) }}"
                   class="btn btn-primary" download>
                    <i class="fas fa-download me-2"></i>Download Video
                </a>
                <button type="button" class="btn btn-warning" onclick="tryVLCPlayer()">
                    <i class="fas fa-play me-2"></i>Open in VLC
                </button>
                <button type="button" class="btn btn-info" onclick="location.reload()">
                    <i class="fas fa-redo me-2"></i>Retry
                </button>
            </div>
        </div>
    `;
}

function tryVLCPlayer() {
    // Try to open with VLC web plugin or external VLC
    const vlcUrl = 'vlc://{{ url_for("stream_file", filename=file_path, _external=True) }}';

    // Try VLC protocol first
    window.location.href = vlcUrl;

    // Fallback: Show instructions
    setTimeout(() => {
        alert(`VLC Player Instructions:

1. Install VLC Media Player if not already installed
2. Copy this URL: {{ url_for('stream_file', filename=file_path, _external=True) }}
3. Open VLC → Media → Open Network Stream
4. Paste the URL and click Play

Alternative: Download the file and open locally in VLC.`);
    }, 1000);
}

function showCodecInfo() {
    const fileExt = '{{ file_path.split(".")[-1].lower() }}';
    const codecSupport = checkCodecSupport();

    alert(`Codec Information:

File Format: ${fileExt.toUpperCase()}
Browser: ${navigator.userAgent.split(' ')[0]}

Supported Formats:
${codecSupport.mp4 ? '✓' : '✗'} MP4 (H.264/AAC)
${codecSupport.webm ? '✓' : '✗'} WebM (VP8/VP9)
${codecSupport.ogg ? '✓' : '✗'} OGG (Theora/Vorbis)

Recommendations:
• Use MP4 with H.264 codec for best compatibility
• Convert MOV/AVI files to MP4 for web playback
• Use VLC player for unsupported formats`);
}

function checkCodecSupport() {
    const video = document.createElement('video');
    return {
        mp4: video.canPlayType('video/mp4; codecs="avc1.42E01E, mp4a.40.2"') !== '',
        webm: video.canPlayType('video/webm; codecs="vp8, vorbis"') !== '',
        ogg: video.canPlayType('video/ogg; codecs="theora, vorbis"') !== ''
    };
}

function updateWatchTime() {
    if (!video.paused) {
        totalWatchTime += 1;
        document.getElementById('watchTime').textContent = formatTime(totalWatchTime);
    }
}

function formatTime(seconds) {
    if (isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return mins.toString().padStart(2, '0') + ':' + secs.toString().padStart(2, '0');
}

function copyFilePath() {
    navigator.clipboard.writeText('{{ file_path }}').then(function() {
        ArchivesConsole.showNotification('File path copied to clipboard!', 'success');
    });
}

function createTaskFromVideo() {
    // Redirect to files page with this file selected
    window.location.href = '{{ url_for("files") }}?file={{ file_path }}';
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.target.tagName.toLowerCase() === 'input') return;
    
    switch(e.code) {
        case 'Space':
            e.preventDefault();
            togglePlayPause();
            break;
        case 'ArrowLeft':
            e.preventDefault();
            skipBackward();
            break;
        case 'ArrowRight':
            e.preventDefault();
            skipForward();
            break;
        case 'KeyF':
            e.preventDefault();
            toggleFullscreen();
            break;
    }
});

// Update session start time
document.getElementById('sessionStart').textContent = sessionStartTime.toLocaleTimeString();

// Auto-play if supported
video.play().catch(function(error) {
    console.log('Auto-play prevented:', error);
});
</script>
{% endblock %}
