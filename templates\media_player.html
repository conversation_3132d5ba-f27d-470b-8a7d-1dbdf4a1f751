{% extends "base.html" %}

{% block title %}Media Player - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-play-circle text-primary me-3"></i>
                    Media Player
                </h1>
                <div class="btn-group">
                    <a href="{{ url_for('files') }}" class="btn btn-secondary">
                        <i class="fas fa-folder-open me-1"></i>Back to Files
                    </a>
                    <button type="button" class="btn btn-outline-primary" onclick="toggleFullscreen()">
                        <i class="fas fa-expand me-1"></i>Fullscreen
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-9">
            <!-- Video Player -->
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="media-player-container" id="playerContainer">
                        <video id="videoPlayer" class="w-100" controls preload="metadata" 
                               style="max-height: 70vh; background: #000;">
                            <source src="{{ url_for('stream_file', filename=file_path) }}" type="video/mp4">
                            <p class="text-center text-white p-4">
                                Your browser does not support the video tag.
                                <br>
                                <a href="{{ url_for('stream_file', filename=file_path) }}" class="btn btn-primary mt-2">
                                    Download Video
                                </a>
                            </p>
                        </video>
                        
                        <!-- Custom Controls Overlay -->
                        <div class="video-controls-overlay" id="controlsOverlay">
                            <div class="d-flex justify-content-center align-items-center gap-3">
                                <button type="button" class="btn btn-outline-light btn-lg" onclick="skipBackward()">
                                    <i class="fas fa-backward"></i>
                                </button>
                                <button type="button" class="btn btn-light btn-lg" id="playPauseBtn" onclick="togglePlayPause()">
                                    <i class="fas fa-play"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-lg" onclick="skipForward()">
                                    <i class="fas fa-forward"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Playback Information -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-info-circle me-2"></i>Playback Information</h6>
                            <div class="small">
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Current Time:</span>
                                    <span id="currentTime">00:00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Duration:</span>
                                    <span id="duration">00:00</span>
                                </div>
                                <div class="d-flex justify-content-between mb-1">
                                    <span>Volume:</span>
                                    <span id="volume">100%</span>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <span>Playback Rate:</span>
                                    <span id="playbackRate">1x</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="fas fa-sliders-h me-2"></i>Quick Controls</h6>
                            <div class="btn-group-vertical w-100" role="group">
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(0.5)">0.5x Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(1)">Normal Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(1.5)">1.5x Speed</button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="setPlaybackRate(2)">2x Speed</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Information Sidebar -->
        <div class="col-lg-3">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-file-video me-2"></i>
                        File Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="small text-muted">File Name:</label>
                        <p class="small mb-0">{{ file_path.split('\\')[-1] if '\\' in file_path else file_path.split('/')[-1] }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">File Path:</label>
                        <p class="small mb-0 text-break">{{ file_path }}</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">File Size:</label>
                        <p class="small mb-0" id="fileSize">Calculating...</p>
                    </div>
                    
                    <div class="mb-3">
                        <label class="small text-muted">Resolution:</label>
                        <p class="small mb-0" id="resolution">Loading...</p>
                    </div>
                    
                    <hr>
                    
                    <div class="d-grid gap-2">
                        {% if current_user.role in ['main_admin', 'executor_public', 'executor_private'] %}
                        <button type="button" class="btn btn-outline-success btn-sm" onclick="createTaskFromVideo()">
                            <i class="fas fa-plus me-1"></i>Create Task
                        </button>
                        {% endif %}
                        
                        <a href="{{ url_for('stream_file', filename=file_path) }}" 
                           class="btn btn-outline-primary btn-sm" download>
                            <i class="fas fa-download me-1"></i>Download
                        </a>
                        
                        <button type="button" class="btn btn-outline-info btn-sm" onclick="copyFilePath()">
                            <i class="fas fa-copy me-1"></i>Copy Path
                        </button>
                    </div>
                </div>
            </div>

            <!-- Playback History -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-history me-2"></i>
                        Session Info
                    </h6>
                </div>
                <div class="card-body">
                    <div class="small">
                        <div class="d-flex justify-content-between mb-1">
                            <span>Started:</span>
                            <span id="sessionStart">{{ moment().format('HH:mm:ss') }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-1">
                            <span>Watch Time:</span>
                            <span id="watchTime">00:00</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>User:</span>
                            <span>{{ current_user.full_name }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.media-player-container {
    position: relative;
    background: #000;
    border-radius: 8px;
    overflow: hidden;
}

.video-controls-overlay {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.media-player-container:hover .video-controls-overlay {
    opacity: 1;
    pointer-events: all;
}

#videoPlayer {
    border-radius: 8px;
}

.btn-group-vertical .btn {
    border-radius: 0;
}

.btn-group-vertical .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-top-right-radius: 0.375rem;
}

.btn-group-vertical .btn:last-child {
    border-bottom-left-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
const video = document.getElementById('videoPlayer');
const playPauseBtn = document.getElementById('playPauseBtn');
let sessionStartTime = new Date();
let totalWatchTime = 0;

// Initialize player
video.addEventListener('loadedmetadata', function() {
    updateDuration();
    updateResolution();
    updateFileSize();
});

video.addEventListener('timeupdate', function() {
    updateCurrentTime();
    updateWatchTime();
});

video.addEventListener('play', function() {
    playPauseBtn.innerHTML = '<i class="fas fa-pause"></i>';
});

video.addEventListener('pause', function() {
    playPauseBtn.innerHTML = '<i class="fas fa-play"></i>';
});

video.addEventListener('volumechange', function() {
    updateVolume();
});

function togglePlayPause() {
    if (video.paused) {
        video.play();
    } else {
        video.pause();
    }
}

function skipBackward() {
    video.currentTime = Math.max(0, video.currentTime - 10);
}

function skipForward() {
    video.currentTime = Math.min(video.duration, video.currentTime + 10);
}

function setPlaybackRate(rate) {
    video.playbackRate = rate;
    document.getElementById('playbackRate').textContent = rate + 'x';
}

function toggleFullscreen() {
    const container = document.getElementById('playerContainer');
    if (document.fullscreenElement) {
        document.exitFullscreen();
    } else {
        container.requestFullscreen();
    }
}

function updateCurrentTime() {
    document.getElementById('currentTime').textContent = formatTime(video.currentTime);
}

function updateDuration() {
    document.getElementById('duration').textContent = formatTime(video.duration);
}

function updateVolume() {
    document.getElementById('volume').textContent = Math.round(video.volume * 100) + '%';
}

function updateResolution() {
    document.getElementById('resolution').textContent = video.videoWidth + 'x' + video.videoHeight;
}

function updateFileSize() {
    // This would need to be passed from the server
    document.getElementById('fileSize').textContent = 'Unknown';
}

function updateWatchTime() {
    if (!video.paused) {
        totalWatchTime += 1;
        document.getElementById('watchTime').textContent = formatTime(totalWatchTime);
    }
}

function formatTime(seconds) {
    if (isNaN(seconds)) return '00:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return mins.toString().padStart(2, '0') + ':' + secs.toString().padStart(2, '0');
}

function copyFilePath() {
    navigator.clipboard.writeText('{{ file_path }}').then(function() {
        ArchivesConsole.showNotification('File path copied to clipboard!', 'success');
    });
}

function createTaskFromVideo() {
    // Redirect to files page with this file selected
    window.location.href = '{{ url_for("files") }}?file={{ file_path }}';
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.target.tagName.toLowerCase() === 'input') return;
    
    switch(e.code) {
        case 'Space':
            e.preventDefault();
            togglePlayPause();
            break;
        case 'ArrowLeft':
            e.preventDefault();
            skipBackward();
            break;
        case 'ArrowRight':
            e.preventDefault();
            skipForward();
            break;
        case 'KeyF':
            e.preventDefault();
            toggleFullscreen();
            break;
    }
});

// Update session start time
document.getElementById('sessionStart').textContent = sessionStartTime.toLocaleTimeString();

// Auto-play if supported
video.play().catch(function(error) {
    console.log('Auto-play prevented:', error);
});
</script>
{% endblock %}
