{% extends "base.html" %}

{% block title %}Profile - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="display-6">
                <i class="fas fa-user-cog text-primary me-3"></i>
                User Profile
            </h1>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>
                        Profile Information
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('update_profile') }}" method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" 
                                           value="{{ current_user.full_name }}" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           value="{{ current_user.email }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" 
                                           value="{{ current_user.username }}" readonly>
                                    <div class="form-text">Username cannot be changed</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="role" class="form-label">Role</label>
                                    <input type="text" class="form-control" id="role" 
                                           value="{{ current_user.role.replace('_', ' ').title() }}" readonly>
                                    <div class="form-text">Role is managed by administrators</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Change Password -->
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-key me-2"></i>
                        Change Password
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ url_for('change_password') }}" method="POST">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="current_password" class="form-label">Current Password</label>
                                    <input type="password" class="form-control" id="current_password" 
                                           name="current_password" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="new_password" class="form-label">New Password</label>
                                    <input type="password" class="form-control" id="new_password" 
                                           name="new_password" required>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="confirm_password" class="form-label">Confirm Password</label>
                                    <input type="password" class="form-control" id="confirm_password" 
                                           name="confirm_password" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>Change Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Profile Summary & Security -->
        <div class="col-lg-4">
            <!-- Profile Summary -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-id-card me-2"></i>
                        Profile Summary
                    </h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="avatar-circle mx-auto mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                        <h5>{{ current_user.full_name }}</h5>
                        <p class="text-muted">{{ current_user.role.replace('_', ' ').title() }}</p>
                    </div>
                    
                    <hr>
                    
                    <div class="small">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Account Status:</span>
                            <span class="badge bg-{{ 'success' if current_user.is_active else 'danger' }}">
                                {{ 'Active' if current_user.is_active else 'Inactive' }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Two-Factor Auth:</span>
                            <span class="badge bg-{{ 'success' if current_user.requires_two_factor else 'secondary' }}">
                                {{ 'Enabled' if current_user.requires_two_factor else 'Disabled' }}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Last Login:</span>
                            <span>
                                {% if current_user.last_login_at %}
                                    {{ current_user.last_login_at.strftime('%Y-%m-%d %H:%M') }}
                                {% else %}
                                    Never
                                {% endif %}
                            </span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Member Since:</span>
                            <span>{{ current_user.created_at.strftime('%Y-%m-%d') if current_user.created_at else 'Unknown' }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-shield-alt me-2"></i>
                        Security Settings
                    </h6>
                </div>
                <div class="card-body">
                    {% if current_user.requires_two_factor %}
                    <div class="alert alert-success">
                        <h6><i class="fas fa-check-circle me-2"></i>2FA Enabled</h6>
                        <p class="mb-2">Your account is protected with two-factor authentication.</p>
                        <form action="{{ url_for('disable_2fa') }}" method="POST" onsubmit="return confirm('Are you sure you want to disable 2FA?')">
                            <div class="mb-2">
                                <input type="password" class="form-control form-control-sm" 
                                       name="password" placeholder="Enter password to disable" required>
                            </div>
                            <button type="submit" class="btn btn-outline-danger btn-sm">
                                <i class="fas fa-times me-1"></i>Disable 2FA
                            </button>
                        </form>
                    </div>
                    {% else %}
                    <div class="alert alert-warning">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>2FA Disabled</h6>
                        <p class="mb-2">Enable two-factor authentication for enhanced security.</p>
                        <a href="{{ url_for('setup_2fa') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-shield-alt me-1"></i>Enable 2FA
                        </a>
                    </div>
                    {% endif %}
                    
                    <hr>
                    
                    <h6>Security Tips:</h6>
                    <ul class="small mb-0">
                        <li>Use a strong, unique password</li>
                        <li>Enable 2FA for private content access</li>
                        <li>Log out when using shared computers</li>
                        <li>Report suspicious activity immediately</li>
                    </ul>
                </div>
            </div>

            <!-- Role Permissions -->
            <div class="card border-0 shadow-sm mt-3">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-user-tag me-2"></i>
                        Role Permissions
                    </h6>
                </div>
                <div class="card-body">
                    {% set role_permissions = {
                        'main_admin': ['User Management', 'System Configuration', 'All Content Access', 'Reports & Analytics'],
                        'executor_public': ['Public Content Processing', 'File Operations', 'Task Management', 'VLC Preview'],
                        'executor_private': ['All Content Processing', 'Private Content Access', 'Enhanced Security', 'Priority Queue'],
                        'cross_checker_public': ['Public Content Review', 'Quality Assessment', 'Feedback System', 'Report Generation'],
                        'cross_checker_private': ['All Content Review', 'Security Validation', 'Compliance Checking', 'Audit Documentation'],
                        'viewer': ['Dashboard Access', 'Statistics Viewing', 'Report Generation', 'System Monitoring']
                    } %}
                    
                    <ul class="small mb-0">
                        {% for permission in role_permissions.get(current_user.role, []) %}
                        <li><i class="fas fa-check text-success me-1"></i>{{ permission }}</li>
                        {% endfor %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = this.value;
    
    if (newPassword !== confirmPassword) {
        this.setCustomValidity('Passwords do not match');
    } else {
        this.setCustomValidity('');
    }
});
</script>
{% endblock %}
