@model ArchivesAssignmentConsole.Web.Controllers.DashboardViewModel
@{
    ViewData["Title"] = "Dashboard";
}

<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="display-4">
                <i class="fas fa-tachometer-alt text-primary me-3"></i>
                Archives Assignment Console
            </h1>
            <div class="text-end">
                <small class="text-muted">Running on Port 5004</small><br>
                <small class="text-muted">@DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")</small>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">@Model.TotalUsers</h4>
                        <p class="card-text">Total Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">@Model.ActiveUsers</h4>
                        <p class="card-text">Active Users</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-user-check fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">@Model.TotalTasks</h4>
                        <p class="card-text">Total Tasks</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-tasks fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="card-title">@Model.PendingTasks</h4>
                        <p class="card-text">Pending Tasks</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Task Status Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="progress mb-3">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: 100%" aria-valuenow="@Model.PendingTasks" aria-valuemin="0" aria-valuemax="100">
                                Pending: @Model.PendingTasks
                            </div>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-info" role="progressbar" style="width: 0%" aria-valuenow="@Model.InProgressTasks" aria-valuemin="0" aria-valuemax="100">
                                In Progress: @Model.InProgressTasks
                            </div>
                        </div>
                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%" aria-valuenow="@Model.CompletedTasks" aria-valuemin="0" aria-valuemax="100">
                                Completed: @Model.CompletedTasks
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>System Status</h6>
                            <ul class="mb-0">
                                <li>Web Interface: <span class="badge bg-success">Online</span></li>
                                <li>Database: <span class="badge bg-success">Connected</span></li>
                                <li>VLC Integration: <span class="badge bg-warning">Web Player</span></li>
                                <li>File System: <span class="badge bg-success">Accessible</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-cogs me-2"></i>
                    Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-primary" type="button">
                        <i class="fas fa-plus me-2"></i>Create New Task
                    </button>
                    <button class="btn btn-info" type="button">
                        <i class="fas fa-folder-open me-2"></i>Browse Files
                    </button>
                    <button class="btn btn-success" type="button">
                        <i class="fas fa-play me-2"></i>Open Media Player
                    </button>
                    <button class="btn btn-warning" type="button">
                        <i class="fas fa-check-double me-2"></i>Cross-Check Queue
                    </button>
                    <hr>
                    <button class="btn btn-secondary" type="button">
                        <i class="fas fa-desktop me-2"></i>Launch Desktop App
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-success" role="alert">
            <h4 class="alert-heading">
                <i class="fas fa-rocket me-2"></i>
                Welcome to Archives Assignment Console!
            </h4>
            <p>This is the web interface running on <strong>port 5004</strong>. You now have access to both:</p>
            <hr>
            <ul class="mb-0">
                <li><strong>Web Interface</strong>: Browser-based access for remote users and basic operations</li>
                <li><strong>Desktop Application</strong>: Full-featured WPF app with embedded VLC player (run start-desktop.bat)</li>
            </ul>
            <p class="mb-0 mt-2">
                <small>Default login: <code>admin</code> / <code>admin123</code></small>
            </p>
        </div>
    </div>
</div>
