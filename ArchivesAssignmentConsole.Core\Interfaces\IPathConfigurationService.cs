using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IPathConfigurationService
    {
        Task<PathConfiguration?> GetPathConfigurationByIdAsync(int id);
        Task<IEnumerable<PathConfiguration>> GetAllPathConfigurationsAsync();
        Task<IEnumerable<PathConfiguration>> GetPathConfigurationsByTypeAsync(PathType type);
        Task<PathConfiguration?> GetDestinationPathAsync(DestinationCategory category);
        Task<PathConfiguration?> GetSourcePathAsync();
        Task<PathConfiguration> CreatePathConfigurationAsync(PathConfiguration pathConfig);
        Task<PathConfiguration> UpdatePathConfigurationAsync(PathConfiguration pathConfig);
        Task<bool> DeletePathConfigurationAsync(int id);
        Task<bool> ValidatePathAsync(string path);
        Task<bool> CreateDirectoryIfNotExistsAsync(string path);
        Task<long> GetDirectorySizeAsync(string path);
        Task<int> GetFileCountInDirectoryAsync(string path);
        Task<IEnumerable<string>> GetSupportedVideoExtensionsAsync();
        Task<bool> IsVideoFileAsync(string filePath);
        Task<string> GetDefaultDestinationPathAsync(DestinationCategory category);
        Task ResetToDefaultPathsAsync(int userId);
    }
}
