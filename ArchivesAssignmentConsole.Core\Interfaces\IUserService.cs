using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IUserService
    {
        Task<User?> GetUserByIdAsync(int id);
        Task<User?> GetUserByUsernameAsync(string username);
        Task<User?> GetUserByEmailAsync(string email);
        Task<IEnumerable<User>> GetAllUsersAsync();
        Task<IEnumerable<User>> GetUsersByRoleAsync(UserRole role);
        Task<IEnumerable<User>> GetActiveUsersAsync();
        Task<User> CreateUserAsync(User user, string password);
        Task<User> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(int id);
        Task<bool> ActivateUserAsync(int id);
        Task<bool> DeactivateUserAsync(int id);
        Task<bool> AssignRoleAsync(int userId, UserRole role);
        Task<bool> ValidatePasswordAsync(int userId, string password);
        Task<bool> UpdatePasswordAsync(int userId, string newPasswordHash);
        Task<IEnumerable<User>> GetUsersForCrossCheckingAsync(UserRole role, bool isPrivateContent);
        Task<Dictionary<UserRole, int>> GetUserCountByRoleAsync();
        Task<IEnumerable<User>> SearchUsersAsync(string searchTerm);
    }
}
