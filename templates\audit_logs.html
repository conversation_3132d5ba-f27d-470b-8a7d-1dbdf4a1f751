{% extends "base.html" %}

{% block title %}Audit Logs - Archives Assignment Console{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="display-6">
                    <i class="fas fa-shield-alt text-primary me-3"></i>
                    Audit Logs
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-primary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button type="button" class="btn btn-primary" onclick="exportLogs()">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <form method="GET" class="row g-3">
                        <div class="col-md-3">
                            <label for="action_filter" class="form-label">Action Type</label>
                            <select class="form-select" id="action_filter" name="action">
                                <option value="">All Actions</option>
                                <option value="login">Login</option>
                                <option value="logout">Logout</option>
                                <option value="login_failed">Failed Login</option>
                                <option value="user_created">User Created</option>
                                <option value="user_modified">User Modified</option>
                                <option value="file_task_created">Task Created</option>
                                <option value="file_task_status_updated">Task Updated</option>
                                <option value="cross_check_created">Cross-Check Created</option>
                                <option value="cross_check_completed">Cross-Check Completed</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="user_filter" class="form-label">User</label>
                            <select class="form-select" id="user_filter" name="user_id">
                                <option value="">All Users</option>
                                <!-- Will be populated with users -->
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="date_from" class="form-label">From Date</label>
                            <input type="date" class="form-control" id="date_from" name="date_from">
                        </div>
                        <div class="col-md-2">
                            <label for="date_to" class="form-label">To Date</label>
                            <input type="date" class="form-control" id="date_to" name="date_to">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i>Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Logs Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list me-2"></i>
                            Security Audit Trail
                        </h5>
                        <small class="text-muted">
                            Showing {{ logs.items | length }} of {{ logs.total }} entries
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Timestamp</th>
                                    <th>User</th>
                                    <th>Action</th>
                                    <th>Entity</th>
                                    <th>Details</th>
                                    <th>IP Address</th>
                                    <th>Security</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs.items %}
                                <tr class="{{ 'table-danger' if log.is_security_event else '' }}">
                                    <td>
                                        <small>{{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                                    </td>
                                    <td>
                                        {% if log.user %}
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-circle me-2" style="width: 24px; height: 24px;">
                                                    <i class="fas fa-user" style="font-size: 10px;"></i>
                                                </div>
                                                <div>
                                                    <small class="fw-bold">{{ log.user.full_name }}</small><br>
                                                    <small class="text-muted">{{ log.user.username }}</small>
                                                </div>
                                            </div>
                                        {% else %}
                                            <small class="text-muted">System</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 
                                            'danger' if 'failed' in log.action or 'error' in log.action else
                                            'warning' if 'login' in log.action or 'logout' in log.action else
                                            'success' if 'created' in log.action or 'completed' in log.action else
                                            'info' if 'updated' in log.action or 'modified' in log.action else
                                            'secondary'
                                        }}">
                                            {{ log.action.replace('_', ' ').title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if log.entity_type %}
                                            <small>
                                                {{ log.entity_type }}
                                                {% if log.entity_id %}#{{ log.entity_id }}{% endif %}
                                            </small>
                                            {% if log.entity_name %}
                                            <br><small class="text-muted">{{ log.entity_name[:30] }}{% if log.entity_name|length > 30 %}...{% endif %}</small>
                                            {% endif %}
                                        {% else %}
                                            <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if log.details %}
                                            <small>{{ log.details[:50] }}{% if log.details|length > 50 %}...{% endif %}</small>
                                        {% else %}
                                            <small class="text-muted">-</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="font-monospace">{{ log.ip_address or '-' }}</small>
                                    </td>
                                    <td>
                                        {% if log.is_security_event %}
                                            <i class="fas fa-exclamation-triangle text-danger" title="Security Event"></i>
                                        {% else %}
                                            <i class="fas fa-check-circle text-success" title="Normal Activity"></i>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                                
                                {% if not logs.items %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-search text-muted fa-3x mb-3"></i>
                                        <h5 class="text-muted">No audit logs found</h5>
                                        <p class="text-muted">Try adjusting your filter criteria.</p>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                {% if logs.pages > 1 %}
                <div class="card-footer bg-white">
                    <nav aria-label="Audit logs pagination">
                        <ul class="pagination justify-content-center mb-0">
                            {% if logs.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('audit_logs', page=logs.prev_num) }}">Previous</a>
                            </li>
                            {% endif %}
                            
                            {% for page_num in logs.iter_pages() %}
                                {% if page_num %}
                                    {% if page_num != logs.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('audit_logs', page=page_num) }}">{{ page_num }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('audit_logs', page=logs.next_num) }}">Next</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Security Summary -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        Activity Summary
                    </h6>
                </div>
                <div class="card-body">
                    {% set total_logs = logs.total %}
                    {% set security_events = logs.items | selectattr('is_security_event') | list | length %}
                    {% set login_events = logs.items | selectattr('action', 'equalto', 'login') | list | length %}
                    {% set failed_logins = logs.items | selectattr('action', 'equalto', 'login_failed') | list | length %}
                    
                    <div class="d-flex justify-content-between mb-2">
                        <span>Total Events:</span>
                        <span class="badge bg-primary">{{ total_logs }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Security Events:</span>
                        <span class="badge bg-{{ 'danger' if security_events > 0 else 'success' }}">{{ security_events }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Successful Logins:</span>
                        <span class="badge bg-success">{{ login_events }}</span>
                    </div>
                    <div class="d-flex justify-content-between">
                        <span>Failed Logins:</span>
                        <span class="badge bg-{{ 'danger' if failed_logins > 0 else 'success' }}">{{ failed_logins }}</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        Security Information
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-shield-alt me-2"></i>Audit Trail</h6>
                        <p class="mb-0 small">All user actions are logged for security and compliance purposes. Logs are retained for 90 days.</p>
                    </div>
                    
                    <h6>Monitored Events:</h6>
                    <ul class="small mb-0">
                        <li>User authentication (login/logout)</li>
                        <li>Failed login attempts</li>
                        <li>User account modifications</li>
                        <li>File operations and task management</li>
                        <li>Cross-check activities</li>
                        <li>System configuration changes</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshLogs() {
    window.location.reload();
}

function exportLogs() {
    // Create CSV data from visible logs
    const table = document.querySelector('table');
    const rows = table.querySelectorAll('tr');
    const csvData = [];
    
    // Header row
    const headers = Array.from(rows[0].querySelectorAll('th')).map(th => th.textContent.trim());
    csvData.push(headers);
    
    // Data rows
    for (let i = 1; i < rows.length; i++) {
        const cells = Array.from(rows[i].querySelectorAll('td')).map(td => {
            return td.textContent.trim().replace(/\n/g, ' ').replace(/,/g, ';');
        });
        if (cells.length > 0) {
            csvData.push(cells);
        }
    }
    
    const csvContent = csvData.map(row => row.join(',')).join('\n');
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'audit_logs_' + new Date().toISOString().split('T')[0] + '.csv';
    a.click();
    window.URL.revokeObjectURL(url);
    
    ArchivesConsole.showNotification('Audit logs exported successfully!', 'success');
}

// Set default date range (last 7 days)
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('date_to').value = today.toISOString().split('T')[0];
    document.getElementById('date_from').value = lastWeek.toISOString().split('T')[0];
});
</script>
{% endblock %}
