#!/usr/bin/env python3
"""
Archives Assignment Console - Complete Production Flask Application
A comprehensive video file organization and processing workflow system
with embedded media player, role-based access control, and cross-checking workflows.
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session, send_file, abort
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import os
import json
import logging
import shutil
import hashlib
import secrets
import pyotp
import qrcode
import io
import base64
from pathlib import Path
from functools import wraps
import mimetypes
import subprocess
import psutil

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = secrets.token_hex(32)  # Generate secure secret key
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///archives.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['MAX_CONTENT_LENGTH'] = 500 * 1024 * 1024  # 500MB max file size
app.config['UPLOAD_FOLDER'] = 'uploads'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)

# Initialize extensions
db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Default configuration
DEFAULT_PATHS = {
    'source_path': r'T:\To_Process\Rough folder\To Process\restore Files',
    'destinations': {
        'miscellaneous': r'T:\To_Process\Rough folder\To Check\Miscellaneous',
        'private_one': r'T:\To_Process\Rough folder\To Check\Private one video',
        'private_two': r'T:\To_Process\Rough folder\To Check\Private Two',
        'to_be_processed': r'T:\To_Process\Rough folder\To Process\restore Files'
    }
}

# Database Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(50), unique=True, nullable=False)
    email = db.Column(db.String(100), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name = db.Column(db.String(100), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='viewer')
    is_active = db.Column(db.Boolean, default=True)
    requires_two_factor = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login_at = db.Column(db.DateTime)
    failed_login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime)
    
    # Relationships
    assigned_tasks = db.relationship('FileTask', foreign_keys='FileTask.assigned_to_user_id', backref='assigned_user')
    created_tasks = db.relationship('FileTask', foreign_keys='FileTask.created_by_user_id', backref='creator')
    audit_logs = db.relationship('AuditLog', backref='user')

class FileTask(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    source_path = db.Column(db.String(500), nullable=False)
    file_name = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.BigInteger)
    file_hash = db.Column(db.String(64))
    category = db.Column(db.String(20), nullable=False)
    destination_path = db.Column(db.String(500))
    status = db.Column(db.String(20), default='pending')
    priority = db.Column(db.String(10), default='normal')
    is_private_content = db.Column(db.Boolean, default=False)
    assigned_to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    created_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    notes = db.Column(db.Text)
    processing_notes = db.Column(db.Text)
    
    # Relationships
    cross_checks = db.relationship('CrossCheck', backref='file_task')
    audit_logs = db.relationship('AuditLog', backref='file_task')

class CrossCheck(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    file_task_id = db.Column(db.Integer, db.ForeignKey('file_task.id'), nullable=False)
    assigned_to_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    reviewed_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    status = db.Column(db.String(20), default='pending')
    result = db.Column(db.String(20))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    review_notes = db.Column(db.Text)
    feedback = db.Column(db.Text)
    quality_score = db.Column(db.Integer)
    requires_second_review = db.Column(db.Boolean, default=False)
    is_second_review = db.Column(db.Boolean, default=False)
    first_review_id = db.Column(db.Integer, db.ForeignKey('cross_check.id'))
    
    # Relationships
    assigned_user = db.relationship('User', foreign_keys=[assigned_to_user_id])
    reviewed_by = db.relationship('User', foreign_keys=[reviewed_by_user_id])
    first_review = db.relationship('CrossCheck', remote_side=[id])

class AuditLog(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(50), nullable=False)
    entity_type = db.Column(db.String(50))
    entity_id = db.Column(db.Integer)
    entity_name = db.Column(db.String(500))
    details = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.String(500))
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    is_security_event = db.Column(db.Boolean, default=False)
    file_task_id = db.Column(db.Integer, db.ForeignKey('file_task.id'))
    cross_check_id = db.Column(db.Integer, db.ForeignKey('cross_check.id'))

class PathConfiguration(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), nullable=False)
    path = db.Column(db.String(500), nullable=False)
    path_type = db.Column(db.String(20), nullable=False)
    category = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    description = db.Column(db.String(200))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    modified_at = db.Column(db.DateTime)
    created_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    modified_by_user_id = db.Column(db.Integer, db.ForeignKey('user.id'))

# User loader for Flask-Login
@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Role-based access decorator
def role_required(*roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                return redirect(url_for('login'))
            if current_user.role not in roles:
                flash('Access denied. Insufficient permissions.', 'error')
                return redirect(url_for('index'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

# Two-factor authentication decorator
def two_factor_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if current_user.requires_two_factor and not session.get('2fa_verified'):
            return redirect(url_for('verify_2fa'))
        return f(*args, **kwargs)
    return decorated_function

# Helper functions
def log_audit(action, entity_type=None, entity_id=None, details=None):
    """Log user actions for audit trail"""
    if current_user.is_authenticated:
        audit_log = AuditLog(
            user_id=current_user.id,
            action=action,
            entity_type=entity_type,
            entity_id=entity_id,
            details=details,
            ip_address=request.remote_addr,
            user_agent=request.user_agent.string
        )
        db.session.add(audit_log)
        db.session.commit()

def get_user_role_display(role):
    """Convert role code to display name"""
    role_map = {
        'main_admin': 'Main Admin/Assigner',
        'executor_public': 'Executor Public',
        'executor_private': 'Executor Private',
        'cross_checker_public': 'Cross-Checker Public',
        'cross_checker_private': 'Cross-Checker Private',
        'viewer': 'Viewer'
    }
    return role_map.get(role, role.title())

def get_task_status_display(status):
    """Convert status code to display name"""
    status_map = {
        'pending': 'Pending',
        'assigned': 'Assigned',
        'in_progress': 'In Progress',
        'completed': 'Completed',
        'cross_check_required': 'Cross-Check Required',
        'cross_check_in_progress': 'Cross-Check In Progress',
        'cross_check_failed': 'Cross-Check Failed',
        'approved': 'Approved',
        'rejected': 'Rejected',
        'on_hold': 'On Hold'
    }
    return status_map.get(status, status.title())

def check_path_exists(path):
    """Check if a path exists and is accessible"""
    try:
        return os.path.exists(path) and os.path.isdir(path)
    except:
        return False

def get_supported_video_extensions():
    """Get list of supported video file extensions"""
    return ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp']

def is_video_file(filename):
    """Check if file is a supported video format"""
    ext = os.path.splitext(filename.lower())[1]
    return ext in get_supported_video_extensions()

# Routes
@app.route('/')
def index():
    """Main dashboard"""
    if not current_user.is_authenticated:
        return redirect(url_for('login'))
    
    # Get dashboard statistics
    total_users = User.query.filter_by(is_active=True).count()
    total_tasks = FileTask.query.count()
    pending_tasks = FileTask.query.filter_by(status='pending').count()
    in_progress_tasks = FileTask.query.filter_by(status='in_progress').count()
    completed_tasks = FileTask.query.filter_by(status='completed').count()
    cross_check_tasks = FileTask.query.filter_by(status='cross_check_required').count()
    
    # Get recent tasks for current user
    recent_tasks = []
    if current_user.role in ['executor_public', 'executor_private']:
        recent_tasks = FileTask.query.filter_by(assigned_to_user_id=current_user.id)\
                                   .order_by(FileTask.created_at.desc()).limit(5).all()
    elif current_user.role in ['cross_checker_public', 'cross_checker_private']:
        recent_cross_checks = CrossCheck.query.filter_by(assigned_to_user_id=current_user.id)\
                                            .order_by(CrossCheck.created_at.desc()).limit(5).all()
        recent_tasks = [cc.file_task for cc in recent_cross_checks]
    
    # Check system status
    system_status = {
        'web_interface': 'Online',
        'database': 'Connected',
        'file_system': 'Checking...',
        'vlc_integration': 'Web Player Ready'
    }
    
    # Check default paths
    source_accessible = check_path_exists(DEFAULT_PATHS['source_path'])
    dest_accessible = all(check_path_exists(path) for path in DEFAULT_PATHS['destinations'].values())
    system_status['file_system'] = 'Accessible' if source_accessible and dest_accessible else 'Limited Access'
    
    return render_template('dashboard.html',
                         total_users=total_users,
                         total_tasks=total_tasks,
                         pending_tasks=pending_tasks,
                         in_progress_tasks=in_progress_tasks,
                         completed_tasks=completed_tasks,
                         cross_check_tasks=cross_check_tasks,
                         recent_tasks=recent_tasks,
                         system_status=system_status,
                         user_role_display=get_user_role_display(current_user.role))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """User login"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember = 'remember' in request.form
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.is_active and check_password_hash(user.password_hash, password):
            # Check if user is locked
            if user.locked_until and user.locked_until > datetime.utcnow():
                flash('Account is temporarily locked. Please try again later.', 'error')
                return render_template('login.html')
            
            # Reset failed attempts on successful login
            user.failed_login_attempts = 0
            user.last_login_at = datetime.utcnow()
            user.locked_until = None
            db.session.commit()
            
            login_user(user, remember=remember)
            log_audit('login')
            
            flash(f'Welcome back, {user.full_name}!', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            # Handle failed login
            if user:
                user.failed_login_attempts += 1
                if user.failed_login_attempts >= 3:
                    user.locked_until = datetime.utcnow() + timedelta(minutes=15)
                db.session.commit()
                log_audit('login_failed', details=f'Failed login attempt for {username}')
            
            flash('Invalid username or password.', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    """User logout"""
    log_audit('logout')
    session.pop('2fa_verified', None)
    logout_user()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('login'))

# Two-Factor Authentication Routes
@app.route('/setup-2fa')
@login_required
def setup_2fa():
    """Setup two-factor authentication"""
    if current_user.requires_two_factor:
        flash('Two-factor authentication is already enabled.', 'info')
        return redirect(url_for('index'))

    # Generate secret
    secret = pyotp.random_base32()
    session['temp_2fa_secret'] = secret

    # Generate QR code
    totp_uri = pyotp.totp.TOTP(secret).provisioning_uri(
        name=current_user.email,
        issuer_name="Archives Assignment Console"
    )

    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(totp_uri)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_buffer.seek(0)

    qr_code_data = base64.b64encode(img_buffer.getvalue()).decode()

    return render_template('setup_2fa.html', secret=secret, qr_code=qr_code_data)

@app.route('/verify-2fa-setup', methods=['POST'])
@login_required
def verify_2fa_setup():
    """Verify and enable 2FA"""
    secret = session.get('temp_2fa_secret')
    code = request.form.get('code')

    if not secret or not code:
        flash('Invalid request.', 'error')
        return redirect(url_for('setup_2fa'))

    totp = pyotp.TOTP(secret)
    if totp.verify(code):
        current_user.two_factor_secret = secret
        current_user.requires_two_factor = True
        db.session.commit()
        session.pop('temp_2fa_secret', None)
        session['2fa_verified'] = True
        log_audit('two_factor_enabled')
        flash('Two-factor authentication has been enabled successfully!', 'success')
        return redirect(url_for('index'))
    else:
        flash('Invalid verification code. Please try again.', 'error')
        return redirect(url_for('setup_2fa'))

@app.route('/verify-2fa', methods=['GET', 'POST'])
@login_required
def verify_2fa():
    """Verify 2FA code during login"""
    if not current_user.requires_two_factor:
        return redirect(url_for('index'))

    if request.method == 'POST':
        code = request.form.get('code')
        if code and current_user.two_factor_secret:
            totp = pyotp.TOTP(current_user.two_factor_secret)
            if totp.verify(code):
                session['2fa_verified'] = True
                log_audit('two_factor_verified')
                flash('Two-factor authentication verified successfully!', 'success')
                return redirect(url_for('index'))
            else:
                flash('Invalid verification code.', 'error')
        else:
            flash('Please enter the verification code.', 'error')

    return render_template('verify_2fa.html')

@app.route('/disable-2fa', methods=['POST'])
@login_required
@two_factor_required
def disable_2fa():
    """Disable two-factor authentication"""
    password = request.form.get('password')
    if password and check_password_hash(current_user.password_hash, password):
        current_user.requires_two_factor = False
        current_user.two_factor_secret = None
        db.session.commit()
        session.pop('2fa_verified', None)
        log_audit('two_factor_disabled')
        flash('Two-factor authentication has been disabled.', 'warning')
    else:
        flash('Invalid password.', 'error')
    return redirect(url_for('profile'))

# File Management Routes
@app.route('/files')
@login_required
def files():
    """File browser page"""
    path = request.args.get('path', DEFAULT_PATHS['source_path'])
    try:
        if not os.path.exists(path):
            flash(f'Path does not exist: {path}', 'warning')
            path = os.getcwd()

        files = []
        directories = []

        for item in os.listdir(path):
            item_path = os.path.join(path, item)
            if os.path.isdir(item_path):
                directories.append({
                    'name': item,
                    'path': item_path,
                    'type': 'directory'
                })
            elif is_video_file(item):
                stat = os.stat(item_path)
                files.append({
                    'name': item,
                    'path': item_path,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime),
                    'type': 'video'
                })

        parent_path = os.path.dirname(path) if path != os.path.dirname(path) else None

        return render_template('files.html',
                             files=files,
                             directories=directories,
                             current_path=path,
                             parent_path=parent_path)
    except PermissionError:
        flash('Permission denied accessing this directory.', 'error')
        return redirect(url_for('index'))
    except Exception as e:
        flash(f'Error accessing directory: {str(e)}', 'error')
        return redirect(url_for('index'))

@app.route('/files/create-task', methods=['POST'])
@login_required
@role_required('main_admin', 'executor_public', 'executor_private')
def create_file_task():
    """Create a new file processing task"""
    file_path = request.form.get('file_path')
    category = request.form.get('category')
    priority = request.form.get('priority', 'normal')
    notes = request.form.get('notes', '')
    assign_to = request.form.get('assign_to')

    if not file_path or not os.path.exists(file_path):
        flash('Invalid file path.', 'error')
        return redirect(url_for('files'))

    # Determine if content is private
    is_private = category in ['private_one', 'private_two']

    # Check user permissions for private content
    if is_private and current_user.role not in ['main_admin', 'executor_private']:
        flash('You do not have permission to create private content tasks.', 'error')
        return redirect(url_for('files'))

    # Create task
    task = FileTask(
        source_path=os.path.dirname(file_path),
        file_name=os.path.basename(file_path),
        file_size=os.path.getsize(file_path),
        file_hash=calculate_file_hash(file_path),
        category=category,
        priority=priority,
        notes=notes,
        is_private_content=is_private,
        created_by_user_id=current_user.id,
        assigned_to_user_id=int(assign_to) if assign_to else None,
        status='assigned' if assign_to else 'pending'
    )

    db.session.add(task)
    db.session.commit()

    log_audit('file_task_created', 'FileTask', task.id, f'Created task for: {task.file_name}')
    flash(f'Task created for {task.file_name}', 'success')
    return redirect(url_for('tasks'))

@app.route('/media-player')
@login_required
def media_player():
    """Media player page"""
    file_path = request.args.get('file')
    if file_path and os.path.exists(file_path) and is_video_file(file_path):
        log_audit('vlc_playback_started', 'File', None, f'Started playback: {file_path}')
        return render_template('media_player.html', file_path=file_path)
    else:
        flash('Invalid video file.', 'error')
        return redirect(url_for('files'))

@app.route('/stream/<path:filename>')
@login_required
def stream_file(filename):
    """Stream video file"""
    try:
        # Security check - ensure file is in allowed directories
        file_path = os.path.abspath(filename)
        allowed_paths = [DEFAULT_PATHS['source_path']] + list(DEFAULT_PATHS['destinations'].values())

        if not any(file_path.startswith(os.path.abspath(path)) for path in allowed_paths):
            abort(403)

        if not os.path.exists(file_path) or not is_video_file(file_path):
            abort(404)

        log_audit('file_viewed', 'File', None, f'Streamed file: {filename}')
        return send_file(file_path)
    except Exception as e:
        logger.error(f'Error streaming file {filename}: {str(e)}')
        abort(500)

# Task Management Routes
@app.route('/tasks')
@login_required
def tasks():
    """Task management page"""
    # Filter tasks based on user role
    if current_user.role == 'main_admin':
        tasks = FileTask.query.order_by(FileTask.created_at.desc()).all()
    elif current_user.role in ['executor_public', 'executor_private']:
        tasks = FileTask.query.filter_by(assigned_to_user_id=current_user.id).order_by(FileTask.created_at.desc()).all()
    elif current_user.role in ['cross_checker_public', 'cross_checker_private']:
        # Get tasks that have cross-checks assigned to this user
        cross_checks = CrossCheck.query.filter_by(assigned_to_user_id=current_user.id).all()
        tasks = [cc.file_task for cc in cross_checks]
    else:  # viewer
        tasks = FileTask.query.order_by(FileTask.created_at.desc()).limit(50).all()

    # Get available users for assignment
    if current_user.role == 'main_admin':
        executors = User.query.filter(User.role.in_(['executor_public', 'executor_private'])).all()
    else:
        executors = []

    return render_template('tasks.html', tasks=tasks, executors=executors)

@app.route('/tasks/<int:task_id>')
@login_required
def task_detail(task_id):
    """Task detail page"""
    task = FileTask.query.get_or_404(task_id)

    # Check permissions
    if (current_user.role not in ['main_admin'] and
        task.assigned_to_user_id != current_user.id and
        current_user.role == 'viewer'):
        flash('Access denied.', 'error')
        return redirect(url_for('tasks'))

    # Check private content access
    if (task.is_private_content and
        current_user.role not in ['main_admin', 'executor_private', 'cross_checker_private']):
        flash('Access denied. Private content requires special permissions.', 'error')
        return redirect(url_for('tasks'))

    cross_checks = CrossCheck.query.filter_by(file_task_id=task.id).order_by(CrossCheck.created_at.desc()).all()

    return render_template('task_detail.html', task=task, cross_checks=cross_checks)

@app.route('/tasks/<int:task_id>/update-status', methods=['POST'])
@login_required
def update_task_status(task_id):
    """Update task status"""
    task = FileTask.query.get_or_404(task_id)
    new_status = request.form.get('status')
    notes = request.form.get('notes', '')

    # Check permissions
    if (current_user.role not in ['main_admin'] and
        task.assigned_to_user_id != current_user.id):
        flash('Access denied.', 'error')
        return redirect(url_for('tasks'))

    old_status = task.status
    task.status = new_status

    if notes:
        task.processing_notes = (task.processing_notes or '') + f'\n[{datetime.now()}] {notes}'

    # Update timestamps
    if new_status == 'in_progress' and not task.started_at:
        task.started_at = datetime.utcnow()
    elif new_status in ['completed', 'approved']:
        task.completed_at = datetime.utcnow()

    db.session.commit()

    log_audit('file_task_status_updated', 'FileTask', task.id,
              f'Status changed from {old_status} to {new_status}')

    # Auto-create cross-check for private content
    if (new_status == 'completed' and task.is_private_content and
        not CrossCheck.query.filter_by(file_task_id=task.id).first()):
        create_cross_check_for_task(task)

    flash(f'Task status updated to {new_status}', 'success')
    return redirect(url_for('task_detail', task_id=task_id))

# Cross-Check Routes
@app.route('/cross-checks')
@login_required
@role_required('main_admin', 'cross_checker_public', 'cross_checker_private')
def cross_checks():
    """Cross-check management page"""
    if current_user.role == 'main_admin':
        cross_checks = CrossCheck.query.order_by(CrossCheck.created_at.desc()).all()
    else:
        # Filter by user role and content type
        if current_user.role == 'cross_checker_private':
            cross_checks = CrossCheck.query.join(FileTask).filter(
                CrossCheck.assigned_to_user_id == current_user.id
            ).order_by(CrossCheck.created_at.desc()).all()
        else:  # cross_checker_public
            cross_checks = CrossCheck.query.join(FileTask).filter(
                CrossCheck.assigned_to_user_id == current_user.id,
                FileTask.is_private_content == False
            ).order_by(CrossCheck.created_at.desc()).all()

    return render_template('cross_checks.html', cross_checks=cross_checks)

@app.route('/cross-checks/<int:cross_check_id>')
@login_required
@role_required('main_admin', 'cross_checker_public', 'cross_checker_private')
def cross_check_detail(cross_check_id):
    """Cross-check detail page"""
    cross_check = CrossCheck.query.get_or_404(cross_check_id)

    # Check permissions
    if (current_user.role not in ['main_admin'] and
        cross_check.assigned_to_user_id != current_user.id):
        flash('Access denied.', 'error')
        return redirect(url_for('cross_checks'))

    # Check private content access
    if (cross_check.file_task.is_private_content and
        current_user.role not in ['main_admin', 'cross_checker_private']):
        flash('Access denied. Private content requires special permissions.', 'error')
        return redirect(url_for('cross_checks'))

    return render_template('cross_check_detail.html', cross_check=cross_check)

@app.route('/cross-checks/<int:cross_check_id>/complete', methods=['POST'])
@login_required
@role_required('main_admin', 'cross_checker_public', 'cross_checker_private')
@two_factor_required
def complete_cross_check(cross_check_id):
    """Complete cross-check review"""
    cross_check = CrossCheck.query.get_or_404(cross_check_id)

    # Check permissions
    if (current_user.role not in ['main_admin'] and
        cross_check.assigned_to_user_id != current_user.id):
        flash('Access denied.', 'error')
        return redirect(url_for('cross_checks'))

    result = request.form.get('result')
    review_notes = request.form.get('review_notes', '')
    feedback = request.form.get('feedback', '')
    quality_score = request.form.get('quality_score')

    cross_check.result = result
    cross_check.review_notes = review_notes
    cross_check.feedback = feedback
    cross_check.quality_score = int(quality_score) if quality_score else None
    cross_check.status = 'completed'
    cross_check.completed_at = datetime.utcnow()
    cross_check.reviewed_by_user_id = current_user.id

    # Update task status based on cross-check result
    task = cross_check.file_task
    if result == 'approved':
        task.status = 'approved'
    elif result == 'rejected':
        task.status = 'rejected'
    elif result == 'needs_revision':
        task.status = 'in_progress'  # Send back for revision

    db.session.commit()

    log_audit('cross_check_completed', 'CrossCheck', cross_check.id,
              f'Cross-check completed with result: {result}')

    flash(f'Cross-check completed with result: {result}', 'success')
    return redirect(url_for('cross_checks'))

@app.route('/cross-checks/create/<int:task_id>', methods=['POST'])
@login_required
@role_required('main_admin')
@two_factor_required
def create_cross_check(task_id):
    """Create cross-check for task"""
    task = FileTask.query.get_or_404(task_id)
    assigned_to = request.form.get('assigned_to')

    if not assigned_to:
        flash('Please select a cross-checker.', 'error')
        return redirect(url_for('task_detail', task_id=task_id))

    # Check if cross-check already exists
    existing = CrossCheck.query.filter_by(file_task_id=task_id).first()
    if existing:
        flash('Cross-check already exists for this task.', 'warning')
        return redirect(url_for('task_detail', task_id=task_id))

    cross_check = CrossCheck(
        file_task_id=task_id,
        assigned_to_user_id=int(assigned_to),
        status='assigned'
    )

    db.session.add(cross_check)
    task.status = 'cross_check_required'
    db.session.commit()

    log_audit('cross_check_created', 'CrossCheck', cross_check.id,
              f'Cross-check created for task: {task.file_name}')

    flash('Cross-check created successfully.', 'success')
    return redirect(url_for('task_detail', task_id=task_id))

# Reports and Analytics
@app.route('/reports')
@login_required
@role_required('main_admin', 'viewer')
def reports():
    """Reports and analytics page"""
    # Task statistics
    total_tasks = FileTask.query.count()
    completed_tasks = FileTask.query.filter_by(status='completed').count()
    pending_tasks = FileTask.query.filter_by(status='pending').count()
    in_progress_tasks = FileTask.query.filter_by(status='in_progress').count()

    # Cross-check statistics
    total_cross_checks = CrossCheck.query.count()
    completed_cross_checks = CrossCheck.query.filter_by(status='completed').count()
    avg_quality_score = db.session.query(db.func.avg(CrossCheck.quality_score)).scalar() or 0

    # User activity
    active_users = User.query.filter_by(is_active=True).count()

    # Recent activity
    recent_tasks = FileTask.query.order_by(FileTask.created_at.desc()).limit(10).all()
    recent_cross_checks = CrossCheck.query.order_by(CrossCheck.created_at.desc()).limit(10).all()

    return render_template('reports.html',
                         total_tasks=total_tasks,
                         completed_tasks=completed_tasks,
                         pending_tasks=pending_tasks,
                         in_progress_tasks=in_progress_tasks,
                         total_cross_checks=total_cross_checks,
                         completed_cross_checks=completed_cross_checks,
                         avg_quality_score=round(avg_quality_score, 2),
                         active_users=active_users,
                         recent_tasks=recent_tasks,
                         recent_cross_checks=recent_cross_checks)

@app.route('/audit-logs')
@login_required
@role_required('main_admin')
@two_factor_required
def audit_logs():
    """Audit logs page"""
    page = request.args.get('page', 1, type=int)
    per_page = 50

    logs = AuditLog.query.order_by(AuditLog.timestamp.desc()).paginate(
        page=page, per_page=per_page, error_out=False
    )

    return render_template('audit_logs.html', logs=logs)

# Helper Functions
def create_cross_check_for_task(task):
    """Auto-create cross-check for completed task"""
    # Find appropriate cross-checker
    if task.is_private_content:
        checker = User.query.filter_by(role='cross_checker_private', is_active=True).first()
    else:
        checker = User.query.filter_by(role='cross_checker_public', is_active=True).first()

    if checker:
        cross_check = CrossCheck(
            file_task_id=task.id,
            assigned_to_user_id=checker.id,
            status='assigned'
        )
        db.session.add(cross_check)
        task.status = 'cross_check_required'
        db.session.commit()

        log_audit('cross_check_auto_created', 'CrossCheck', cross_check.id,
                  f'Auto-created cross-check for task: {task.file_name}')

def calculate_file_hash(file_path):
    """Calculate SHA-256 hash of file"""
    try:
        hash_sha256 = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_sha256.update(chunk)
        return hash_sha256.hexdigest()
    except Exception:
        return None

# User Management Routes
@app.route('/users')
@login_required
@role_required('main_admin')
@two_factor_required
def users():
    """User management page"""
    users = User.query.all()
    return render_template('users.html', users=users)

@app.route('/users/create', methods=['GET', 'POST'])
@login_required
@role_required('main_admin')
@two_factor_required
def create_user():
    """Create new user"""
    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        full_name = request.form.get('full_name')
        role = request.form.get('role')
        password = request.form.get('password')
        requires_2fa = 'requires_2fa' in request.form

        # Validation
        if User.query.filter_by(username=username).first():
            flash('Username already exists.', 'error')
            return render_template('create_user.html')

        if User.query.filter_by(email=email).first():
            flash('Email already exists.', 'error')
            return render_template('create_user.html')

        # Create user
        new_user = User(
            username=username,
            email=email,
            full_name=full_name,
            role=role,
            password_hash=generate_password_hash(password),
            requires_two_factor=requires_2fa,
            is_active=True
        )

        db.session.add(new_user)
        db.session.commit()

        log_audit('user_created', 'User', new_user.id, f'Created user: {username}')
        flash(f'User {username} created successfully!', 'success')
        return redirect(url_for('users'))

    return render_template('create_user.html')

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
@role_required('main_admin')
@two_factor_required
def edit_user(user_id):
    """Edit user"""
    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        user.full_name = request.form.get('full_name')
        user.email = request.form.get('email')
        user.role = request.form.get('role')
        user.is_active = 'is_active' in request.form
        user.requires_two_factor = 'requires_2fa' in request.form

        # Reset password if provided
        new_password = request.form.get('new_password')
        if new_password:
            user.password_hash = generate_password_hash(new_password)
            user.failed_login_attempts = 0
            user.locked_until = None

        db.session.commit()
        log_audit('user_modified', 'User', user.id, f'Modified user: {user.username}')
        flash(f'User {user.username} updated successfully!', 'success')
        return redirect(url_for('users'))

    return render_template('edit_user.html', user=user)

@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
@role_required('main_admin')
@two_factor_required
def delete_user(user_id):
    """Delete user"""
    user = User.query.get_or_404(user_id)

    if user.id == current_user.id:
        flash('You cannot delete your own account.', 'error')
        return redirect(url_for('users'))

    username = user.username
    db.session.delete(user)
    db.session.commit()

    log_audit('user_deleted', 'User', user_id, f'Deleted user: {username}')
    flash(f'User {username} deleted successfully!', 'success')
    return redirect(url_for('users'))

# Profile Management
@app.route('/profile')
@login_required
def profile():
    """User profile page"""
    return render_template('profile.html')

@app.route('/profile/update', methods=['POST'])
@login_required
def update_profile():
    """Update user profile"""
    current_user.full_name = request.form.get('full_name')
    current_user.email = request.form.get('email')
    db.session.commit()

    log_audit('profile_updated')
    flash('Profile updated successfully!', 'success')
    return redirect(url_for('profile'))

@app.route('/profile/change-password', methods=['POST'])
@login_required
def change_password():
    """Change user password"""
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')

    if not check_password_hash(current_user.password_hash, current_password):
        flash('Current password is incorrect.', 'error')
        return redirect(url_for('profile'))

    if new_password != confirm_password:
        flash('New passwords do not match.', 'error')
        return redirect(url_for('profile'))

    current_user.password_hash = generate_password_hash(new_password)
    current_user.failed_login_attempts = 0
    current_user.locked_until = None
    db.session.commit()

    log_audit('password_changed')
    flash('Password changed successfully!', 'success')
    return redirect(url_for('profile'))

if __name__ == '__main__':
    # Create necessary directories
    os.makedirs('logs', exist_ok=True)
    os.makedirs('uploads', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)
    os.makedirs('templates', exist_ok=True)

    # Create database tables
    with app.app_context():
        db.create_all()

        # Create default users if not exist
        if not User.query.filter_by(username='admin').first():
            # Main Admin
            admin_user = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                full_name='System Administrator',
                role='main_admin',
                is_active=True
            )
            db.session.add(admin_user)

            # Sample users for each role
            sample_users = [
                ('executor_pub', '<EMAIL>', 'Public Executor', 'executor_public'),
                ('executor_priv', '<EMAIL>', 'Private Executor', 'executor_private'),
                ('checker_pub', '<EMAIL>', 'Public Cross-Checker', 'cross_checker_public'),
                ('checker_priv', '<EMAIL>', 'Private Cross-Checker', 'cross_checker_private'),
                ('viewer', '<EMAIL>', 'System Viewer', 'viewer')
            ]

            for username, email, full_name, role in sample_users:
                user = User(
                    username=username,
                    email=email,
                    password_hash=generate_password_hash('password123'),
                    full_name=full_name,
                    role=role,
                    is_active=True,
                    requires_two_factor=(role in ['executor_private', 'cross_checker_private'])
                )
                db.session.add(user)

            db.session.commit()
            logger.info('Created default users with roles')

        # Create default path configurations
        if not PathConfiguration.query.first():
            admin_user = User.query.filter_by(username='admin').first()
            default_paths = [
                ('Source Path', DEFAULT_PATHS['source_path'], 'source', None),
                ('Miscellaneous', DEFAULT_PATHS['destinations']['miscellaneous'], 'destination', 'miscellaneous'),
                ('Private One', DEFAULT_PATHS['destinations']['private_one'], 'destination', 'private_one'),
                ('Private Two', DEFAULT_PATHS['destinations']['private_two'], 'destination', 'private_two'),
                ('To Be Processed', DEFAULT_PATHS['destinations']['to_be_processed'], 'destination', 'to_be_processed')
            ]

            for name, path, path_type, category in default_paths:
                path_config = PathConfiguration(
                    name=name,
                    path=path,
                    path_type=path_type,
                    category=category,
                    is_active=True,
                    created_by_user_id=admin_user.id
                )
                db.session.add(path_config)

            db.session.commit()
            logger.info('Created default path configurations')

    # Run the application on port 5004
    logger.info('Starting Archives Assignment Console on port 5004...')
    logger.info('Default users created:')
    logger.info('  admin/admin123 (Main Admin)')
    logger.info('  executor_pub/password123 (Public Executor)')
    logger.info('  executor_priv/password123 (Private Executor - 2FA required)')
    logger.info('  checker_pub/password123 (Public Cross-Checker)')
    logger.info('  checker_priv/password123 (Private Cross-Checker - 2FA required)')
    logger.info('  viewer/password123 (Viewer)')
    app.run(host='0.0.0.0', port=5004, debug=True)
