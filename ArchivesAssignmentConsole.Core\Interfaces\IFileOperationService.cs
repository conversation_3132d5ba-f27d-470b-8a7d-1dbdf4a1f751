using ArchivesAssignmentConsole.Core.Models;

namespace ArchivesAssignmentConsole.Core.Interfaces
{
    public interface IFileOperationService
    {
        Task<IEnumerable<string>> ScanDirectoryAsync(string path, string[] supportedExtensions);
        Task<FileTask> CreateFileTaskAsync(string sourcePath, string fileName, DestinationCategory category, int createdByUserId, bool isPrivateContent = false);
        Task<IEnumerable<FileTask>> CreateBatchFileTasksAsync(IEnumerable<string> filePaths, DestinationCategory category, int createdByUserId, bool isPrivateContent = false);
        Task<FileTask?> GetFileTaskByIdAsync(int id);
        Task<IEnumerable<FileTask>> GetFileTasksAsync(TaskStatus? status = null, int? assignedToUserId = null, bool? isPrivateContent = null);
        Task<IEnumerable<FileTask>> GetPendingTasksAsync();
        Task<IEnumerable<FileTask>> GetTasksForUserAsync(int userId);
        Task<FileTask> AssignTaskAsync(int taskId, int assignedToUserId);
        Task<FileTask> UpdateTaskStatusAsync(int taskId, TaskStatus status, string? notes = null);
        Task<bool> MoveFileAsync(int taskId, string destinationPath);
        Task<bool> CopyFileAsync(int taskId, string destinationPath);
        Task<bool> DeleteFileAsync(int taskId);
        Task<bool> ValidateFileIntegrityAsync(int taskId);
        Task<string> CalculateFileHashAsync(string filePath);
        Task<long> GetFileSizeAsync(string filePath);
        Task<bool> FileExistsAsync(string filePath);
        Task<string> GetDestinationPathAsync(DestinationCategory category);
        Task<IEnumerable<FileTask>> GetTasksByPriorityAsync(TaskPriority priority);
        Task<FileTask> SetTaskPriorityAsync(int taskId, TaskPriority priority);
        Task<Dictionary<TaskStatus, int>> GetTaskCountByStatusAsync();
        Task<IEnumerable<FileTask>> SearchTasksAsync(string searchTerm);
    }
}
